import { render, screen } from '@testing-library/react'
import { Logo } from '@/components/logo'

describe('Logo Component', () => {
  it('renders the logo with default size', () => {
    render(<Logo />)
    
    const logo = screen.getByRole('img', { name: /renovhub/i })
    expect(logo).toBeInTheDocument()
  })

  it('renders with custom size', () => {
    render(<Logo size="lg" />)
    
    const logo = screen.getByRole('img', { name: /renovhub/i })
    expect(logo).toBeInTheDocument()
  })

  it('renders with custom className', () => {
    const customClass = 'custom-logo-class'
    render(<Logo className={customClass} />)
    
    const logoContainer = screen.getByRole('img', { name: /renovhub/i }).parentElement
    expect(logoContainer).toHaveClass(customClass)
  })

  it('has correct accessibility attributes', () => {
    render(<Logo />)
    
    const logo = screen.getByRole('img', { name: /renovhub/i })
    expect(logo).toHaveAttribute('alt', 'RenovHub')
  })
})
