"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Badge } from "@/components/ui/badge"
import { Bell, MessageCircle, DollarSign, CheckCircle, Clock, Trash2, BookMarkedIcon as MarkAs<PERSON><PERSON>, Hammer, Info } from "lucide-react"

interface Notification {
  id: string
  type: "message" | "payment" | "milestone" | "bid" | "system"
  title: string
  message: string
  time: string
  read: boolean
  project?: string
}

export default function NotificationsPage() {
  const { user } = useUser()
  const [filter, setFilter] = useState("all")
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      type: "milestone",
      title: "Milestone Completed",
      message: "Cabinet installation has been completed for your Kitchen Remodel project",
      time: "2 hours ago",
      read: false,
      project: "Kitchen Remodel",
    },
    {
      id: "2",
      type: "message",
      title: "New Message",
      message: "<PERSON>'s Kitchen Experts sent you a message about material delivery",
      time: "5 hours ago",
      read: false,
      project: "Kitchen Remodel",
    },
    {
      id: "3",
      type: "bid",
      title: "New Bid Received",
      message: "Premium Home Solutions submitted a bid for your Bathroom Renovation",
      time: "1 day ago",
      read: true,
      project: "Bathroom Renovation",
    },
    {
      id: "4",
      type: "payment",
      title: "Payment Released",
      message: "Milestone payment of $3,250 has been released to Mike's Kitchen Experts",
      time: "2 days ago",
      read: true,
      project: "Kitchen Remodel",
    },
    {
      id: "5",
      type: "system",
      title: "Project Update",
      message: "Your Kitchen Remodel project is now 65% complete",
      time: "3 days ago",
      read: true,
      project: "Kitchen Remodel",
    },
  ])

  const filters = [
    { id: "all", label: "All", count: notifications.length },
    { id: "unread", label: "Unread", count: notifications.filter((n) => !n.read).length },
    { id: "message", label: "Messages", count: notifications.filter((n) => n.type === "message").length },
    { id: "milestone", label: "Milestones", count: notifications.filter((n) => n.type === "milestone").length },
    { id: "payment", label: "Payments", count: notifications.filter((n) => n.type === "payment").length },
  ]

  const getIcon = (type: string) => {
    switch (type) {
      case "message":
        return <MessageCircle className="h-5 w-5 text-blue-600" />
      case "payment":
        return <DollarSign className="h-5 w-5 text-green-600" />
      case "milestone":
        return <CheckCircle className="h-5 w-5 text-purple-600" />
      case "bid":
        return <Clock className="h-5 w-5 text-orange-600" />
      default:
        return <Bell className="h-5 w-5 text-slate-600" />
    }
  }

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "all") return true
    if (filter === "unread") return !notification.read
    return notification.type === filter
  })

  const markAsRead = (id: string) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })))
  }

  const deleteNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Enhanced Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-12 gap-6">
          <div className="space-y-2">
            <h1 className="text-2xl lg:text-3xl font-bold text-slate-900">Notifications</h1>
            <p className="text-lg text-slate-600 max-w-2xl">Stay updated on your projects, messages, and important updates</p>
          </div>

          {notifications.some((n) => !n.read) && (
            <Button
              onClick={markAllAsRead}
              variant="outline"
              size="sm"
              className="bg-white border-slate-200 hover:border-slate-300"
            >
              <MarkAsRead className="h-4 w-4 mr-2" />
              Mark All Read
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-slate-100 rounded-xl p-4 shadow-sm">
              <h3 className="font-medium text-slate-900 mb-4">Filter</h3>

              <div className="space-y-2">
                {filters.map((filterOption) => (
                  <button
                    key={filterOption.id}
                    onClick={() => setFilter(filterOption.id)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                      filter === filterOption.id ? "bg-slate-900 text-white" : "hover:bg-slate-50 text-slate-700"
                    }`}
                  >
                    <span className="text-sm font-medium">{filterOption.label}</span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        filter === filterOption.id ? "bg-white/20 text-white" : "bg-slate-100 text-slate-600"
                      }`}
                    >
                      {filterOption.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <div className="lg:col-span-3">
            <div className="space-y-4">
              {filteredNotifications.length === 0 ? (
                <div className="bg-white border border-slate-100 rounded-xl p-12 text-center shadow-sm">
                  <Bell className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-900 mb-2">No notifications</h3>
                  <p className="text-slate-500">You're all caught up!</p>
                </div>
              ) : (
                filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-6 shadow-lg transition-all hover:shadow-xl hover:scale-[1.02] ${
                      !notification.read ? "ring-2 ring-blue-500/20 bg-blue-50/30" : ""
                    }`}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">{getIcon(notification.type)}</div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className={`text-lg font-bold ${!notification.read ? "text-slate-900" : "text-slate-700"}`}>
                              {notification.title}
                            </h3>
                            {notification.project && <p className="text-sm text-slate-500">{notification.project}</p>}
                          </div>

                          <div className="flex items-center space-x-2">
                            {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                            <span className="text-sm text-slate-400">{notification.time}</span>
                          </div>
                        </div>

                        <p className="text-slate-600 leading-relaxed mb-4">{notification.message}</p>

                        <div className="flex items-center space-x-3">
                          {!notification.read && (
                            <Button
                              onClick={() => markAsRead(notification.id)}
                              size="sm"
                              variant="outline"
                              className="bg-transparent border-slate-200 hover:border-slate-300"
                            >
                              Mark as Read
                            </Button>
                          )}

                          <Button
                            onClick={() => deleteNotification(notification.id)}
                            size="sm"
                            variant="ghost"
                            className="text-slate-400 hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}
