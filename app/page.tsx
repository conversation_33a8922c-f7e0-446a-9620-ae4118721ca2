"use client"

import { useState, useRef, useEffect } from "react"
import { ArrowRight, Camera, Mic, MapPin, ChefHat, Bath, Hammer, Palette } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { Logo } from "@/components/logo"
import { useUser } from "@/contexts/user-context"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/toast-system"
import Link from "next/link"

export default function HomePage() {
  const [projectDescription, setProjectDescription] = useState("")
  const [isFocused, setIsFocused] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [location, setLocation] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { user } = useUser()
  const router = useRouter()
  const { success, error, info } = useToast()

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [projectDescription])

  const handleStartProject = () => {
    if (projectDescription.trim()) {
      const params = new URLSearchParams({
        description: projectDescription,
        ...(location && { location }),
        ...(uploadedImages.length > 0 && { hasImages: 'true' })
      })
      router.push(`/project/create?${params.toString()}`)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      const newImages = [...uploadedImages, ...files].slice(0, 5) // Limit to 5 images
      setUploadedImages(newImages)
      success(`${files.length} photo${files.length > 1 ? 's' : ''} added successfully`)

      if (files.length + uploadedImages.length > 5) {
        info("Maximum 5 photos allowed. Extra photos were not added.")
      }
    }
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const handleLocationRequest = () => {
    setIsGettingLocation(true)

    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            // Reverse geocoding to get address
            const response = await fetch(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`
            )

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()

            // Check if we got valid location data
            if (data && (data.city || data.locality || data.principalSubdivision)) {
              const city = data.city || data.locality || 'Unknown City'
              const state = data.principalSubdivision || data.countryName || 'Unknown State'
              const locationString = `${city}, ${state}`
              setLocation(locationString)
              success(`Location set to ${locationString}`)
            } else {
              // Fallback to coordinates if no readable address
              const coords = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
              setLocation(coords)
              info(`Location set to coordinates: ${coords}`)
            }
          } catch (error) {
            // Better error handling
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            console.warn('Geocoding failed:', errorMessage)

            // Fallback to coordinates
            const coords = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
            setLocation(coords)
            info(`Location set to coordinates: ${coords}`)
          } finally {
            setIsGettingLocation(false)
          }
        },
        (geolocationError) => {
          const errorMessages = {
            1: 'Location access denied. Please enable location services.',
            2: 'Location unavailable. Please check your connection.',
            3: 'Location request timed out. Please try again.'
          }

          const errorCode = geolocationError.code as keyof typeof errorMessages
          const errorMessage = errorMessages[errorCode] || 'Unable to get your location.'

          console.warn('Geolocation error:', errorMessage)
          setIsGettingLocation(false)
          error(errorMessage)

          // Fallback to manual input
          setTimeout(() => {
            const manualLocation = prompt('Please enter your location (City, State):')
            if (manualLocation && manualLocation.trim()) {
              setLocation(manualLocation.trim())
              success(`Location set to ${manualLocation.trim()}`)
            }
          }, 100)
        }
      )
    } else {
      setIsGettingLocation(false)
      error('Geolocation not supported in your browser.')

      setTimeout(() => {
        const manualLocation = prompt('Please enter your location (City, State):')
        if (manualLocation && manualLocation.trim()) {
          setLocation(manualLocation.trim())
          success(`Location set to ${manualLocation.trim()}`)
        }
      }, 100)
    }
  }

  const handleVoiceInput = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
      const recognition = new SpeechRecognition()

      recognition.continuous = false
      recognition.interimResults = false
      recognition.lang = 'en-US'

      recognition.onstart = () => {
        setIsRecording(true)
      }

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        setProjectDescription(prev => prev + (prev ? ' ' : '') + transcript)
        setIsRecording(false)
        success('Voice input added successfully')
      }

      recognition.onerror = () => {
        setIsRecording(false)
        error('Voice recognition failed. Please try again.')
      }

      recognition.onend = () => {
        setIsRecording(false)
      }

      recognition.start()
    } else {
      error('Speech recognition is not supported in your browser.')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="container-premium py-16 lg:py-24">
          {/* Clean Hero */}
          <div className="text-center mb-16">
            <div className="space-y-6">
              {/* Clean Hero Title */}
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 text-balance max-w-4xl mx-auto leading-tight">
                Renovate with{' '}
                <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                  Confidence
                </span>
              </h1>

              <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed mt-6">
                Get free quotes from verified contractors in minutes. Compare proposals, read reviews, and hire the perfect professional for your renovation project.
              </p>



              {/* Key Benefits */}
              <div className="flex flex-wrap items-center justify-center gap-6 text-sm pt-4">
                <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-xl border border-slate-200/60 shadow-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-slate-700">Free quotes</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-xl border border-slate-200/60 shadow-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-slate-700">Verified professionals</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-xl border border-slate-200/60 shadow-lg">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-slate-700">No commitment</span>
                </div>
              </div>
            </div>
          </div>

          {/* Clean Project Input Section */}
          <div className="max-w-4xl mx-auto">
            <div className={`bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg transition-all duration-300 ${isFocused ? "shadow-xl hover:scale-[1.02] border-blue-500/20" : ""}`}>
              <div className="text-center mb-8">
                <h2 className="text-2xl lg:text-3xl font-bold text-slate-900 mb-4">
                  Describe your <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">renovation project</span>
                </h2>
                <p className="text-slate-600 mb-6">
                  Tell us what you want to renovate and we'll connect you with qualified contractors in your area.
                </p>
              </div>

              {/* Clean Textarea - Enhanced Mobile */}
              <div className="relative">
                <Textarea
                  ref={textareaRef}
                  placeholder="I want to transform my kitchen with modern cabinets, quartz countertops, and premium appliances..."
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  className="w-full min-h-[140px] sm:min-h-[120px] p-4 sm:p-6 border border-slate-200/60 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base resize-none bg-white/80 backdrop-blur-sm placeholder:text-slate-400 touch-target-enhanced"
                  maxLength={500}
                  aria-label="Describe your renovation project"
                  aria-describedby="project-description-help"
                />
                <div className="absolute bottom-3 right-3 text-xs text-slate-400" aria-live="polite">
                  <span className={projectDescription.length > 450 ? 'text-amber-600 font-medium' : ''}>{projectDescription.length}</span>/500
                </div>
                <div id="project-description-help" className="sr-only">
                  Describe your renovation project in detail. Maximum 500 characters.
                </div>
              </div>

              {/* Photo preview */}
              {uploadedImages.length > 0 && (
                <div className="mt-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-medium text-slate-700">
                        {uploadedImages.length} photo{uploadedImages.length > 1 ? 's' : ''}
                      </span>
                    </div>
                    <span className="text-xs text-slate-500">
                      {5 - uploadedImages.length} remaining
                    </span>
                  </div>
                  <div className="grid grid-cols-5 gap-2">
                    {uploadedImages.map((file, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Upload ${index + 1}`}
                          className="w-full aspect-square object-cover rounded-md border border-slate-200"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-1 -right-1 w-5 h-5 bg-slate-900 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Location display */}
              {location && (
                <div className="mt-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-4 w-4 text-slate-600" />
                      <div>
                        <p className="text-sm font-medium text-slate-700">Location</p>
                        <p className="text-slate-900">{location}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setLocation("")}
                      className="text-slate-600 hover:text-slate-900 text-sm px-2 py-1 rounded hover:bg-slate-200 transition-colors"
                    >
                      Change
                    </button>
                  </div>
                </div>
              )}

              {/* Clean action buttons */}
              <div className="mt-6 pt-6 border-t border-slate-200/60">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className={`flex items-center space-x-2 px-4 py-3 rounded-lg border transition-all duration-200 touch-target-enhanced ${
                        uploadedImages.length > 0
                          ? "border-blue-200/60 bg-blue-50 text-blue-700 hover:bg-blue-100"
                          : "border-slate-200/60 text-slate-600 hover:border-slate-300 hover:bg-slate-50"
                      }`}
                      aria-label={`Add photos to your project${uploadedImages.length > 0 ? ` (${uploadedImages.length} added)` : ''}`}
                    >
                      <Camera className="h-4 w-4" aria-hidden="true" />
                      <span className="text-sm font-medium">
                        Photos {uploadedImages.length > 0 && `(${uploadedImages.length})`}
                      </span>
                    </button>

                    <button
                      onClick={handleVoiceInput}
                      disabled={isRecording}
                      className={`flex items-center space-x-2 px-4 py-3 rounded-lg border transition-all duration-200 touch-target-enhanced ${
                        isRecording
                          ? "border-red-200/60 bg-red-50 text-red-700"
                          : "border-slate-200/60 text-slate-600 hover:border-slate-300 hover:bg-slate-50"
                      }`}
                    >
                      <Mic className={`h-4 w-4 ${isRecording ? 'animate-pulse' : ''}`} />
                      <span className="text-sm font-medium">
                        {isRecording ? 'Recording...' : 'Voice'}
                      </span>
                    </button>

                    <button
                      onClick={handleLocationRequest}
                      disabled={isGettingLocation}
                      className={`flex items-center space-x-2 px-4 py-3 rounded-lg border transition-all duration-200 touch-target-enhanced ${
                        location
                          ? "border-green-200/60 bg-green-50 text-green-700"
                          : "border-slate-200/60 text-slate-600 hover:border-slate-300 hover:bg-slate-50"
                      }`}
                    >
                      <MapPin className={`h-4 w-4 ${isGettingLocation ? 'animate-spin' : ''}`} />
                      <span className="text-sm font-medium">
                        {isGettingLocation ? 'Getting...' : location ? 'Located' : 'Location'}
                      </span>
                    </button>
                  </div>

                  {projectDescription && (
                    <button
                      onClick={handleStartProject}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center space-x-2 hover:scale-105 touch-target-enhanced"
                      aria-label="Start your renovation project"
                    >
                      <span>Get Free Quotes</span>
                      <ArrowRight className="h-4 w-4" aria-hidden="true" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Get Inspired Section */}
          {!projectDescription && (
            <div className="mt-20 -mx-8 lg:-mx-16 xl:-mx-24">
              {/* Full Width Faded Background */}
              <div className="bg-gradient-to-br from-slate-50/80 via-blue-50/40 to-slate-50/80 py-16">
                <div className="container-premium">
                  <div className="text-center mb-12">
                    <h3 className="text-2xl lg:text-3xl font-bold text-slate-900 mb-4">Get Inspired</h3>
                    <p className="text-lg text-slate-600 mb-2 max-w-2xl mx-auto">
                      Browse popular renovation projects or start with one of these ideas
                    </p>
                    <p className="text-sm text-slate-500 max-w-xl mx-auto">
                      Click any category to automatically fill your project description
                    </p>
                  </div>

              {/* Minimized Project Categories */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 max-w-5xl mx-auto mb-16">
                {[
                  {
                    name: "Kitchen Renovation",
                    icon: ChefHat,
                    description: "Modern kitchens with premium finishes",
                    color: "from-orange-500 to-red-500",
                    bgColor: "bg-orange-50",
                    borderColor: "border-orange-200/60"
                  },
                  {
                    name: "Bathroom Remodel",
                    icon: Bath,
                    description: "Spa-like bathrooms with luxury touches",
                    color: "from-blue-500 to-cyan-500",
                    bgColor: "bg-blue-50",
                    borderColor: "border-blue-200/60"
                  },
                  {
                    name: "Flooring Installation",
                    icon: Hammer,
                    description: "Hardwood, tile, and luxury vinyl",
                    color: "from-amber-500 to-yellow-500",
                    bgColor: "bg-amber-50",
                    borderColor: "border-amber-200/60"
                  },
                  {
                    name: "House Painting",
                    icon: Palette,
                    description: "Interior and exterior painting services",
                    color: "from-purple-500 to-pink-500",
                    bgColor: "bg-purple-50",
                    borderColor: "border-purple-200/60"
                  }
                ].map((example, index) => (
                  <button
                    key={index}
                    onClick={() => setProjectDescription(`I want to ${example.name.toLowerCase()} with ${example.description.toLowerCase()}`)}
                    className={`group p-5 bg-white/80 backdrop-blur-sm border ${example.borderColor} rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300 text-center hover:bg-white`}
                  >
                    <div className={`w-12 h-12 bg-gradient-to-r ${example.color} rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl group-hover:scale-105 transition-all duration-300`}>
                      <example.icon className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="text-base font-bold text-slate-900 mb-2 group-hover:text-slate-700 transition-colors">{example.name}</h4>
                    <p className="text-xs text-slate-600 leading-relaxed mb-2">{example.description}</p>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-xs text-blue-600 font-medium">Click to use →</span>
                    </div>
                  </button>
                ))}
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Enhanced How It Works Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-slate-200/60 max-w-6xl mx-auto shadow-lg">
                <div className="text-center mb-8">
                  <h4 className="text-2xl font-bold text-slate-900 mb-4">How RenovHub Works</h4>
                  <p className="text-slate-600 max-w-2xl mx-auto">
                    Get started with your renovation project in four simple steps
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 relative">
                  {[
                    {
                      step: "1",
                      title: "Describe Your Project",
                      description: "Tell us about your renovation vision, budget, and timeline",
                      color: "bg-blue-600"
                    },
                    {
                      step: "2",
                      title: "Get Matched",
                      description: "We connect you with verified contractors in your area",
                      color: "bg-purple-600"
                    },
                    {
                      step: "3",
                      title: "Accept Offer",
                      description: "Review proposals and choose the best contractor for your project",
                      color: "bg-orange-600"
                    },
                    {
                      step: "4",
                      title: "Start Building",
                      description: "Manage your project and bring your dream to life",
                      color: "bg-green-600"
                    }
                  ].map((step, index) => (
                    <div key={index} className="text-center relative">
                      <div className={`w-16 h-16 ${step.color} rounded-xl flex items-center justify-center text-white font-bold text-lg mx-auto mb-4 shadow-lg`}>
                        {step.step}
                      </div>
                      <h5 className="text-lg font-bold text-slate-900 mb-3">{step.title}</h5>
                      <p className="text-slate-600 leading-relaxed text-sm">{step.description}</p>

                      {/* Stylish Faded Arrow */}
                      {index < 3 && (
                        <div className="hidden md:block absolute top-8 -right-3 transform translate-x-full">
                          <svg
                            className="w-6 h-6 text-slate-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13 7l5 5m0 0l-5 5m5-5H6"
                              className="opacity-60"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

          {/* Clean CTA Section */}
          <div className="text-center mt-16 mb-16">
            <div className="space-y-6">
              <h3 className="text-2xl lg:text-3xl font-bold text-slate-900">
                Ready to transform your space?
              </h3>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
                Start your renovation journey today with trusted professionals in your area. Get free quotes with no commitment.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link href="/project/create">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-10 py-4 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 touch-target-enhanced">
                    Start Your Project Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/contractors">
                  <Button variant="outline" size="lg" className="bg-white/80 backdrop-blur-sm border-slate-200/60 hover:border-slate-300 hover:bg-white px-10 py-4 transition-all duration-200 hover:scale-105 touch-target-enhanced">
                    Browse Contractors
                  </Button>
                </Link>
              </div>
            </div>
          </div>

        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}
