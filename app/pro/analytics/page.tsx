"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { PageHeader } from "@/components/breadcrumb"
import { ProRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  TrendingUp,
  TrendingDown,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Loader2,
  Star,
  BarChart3,
  PieChart,
  Activity
} from "lucide-react"

interface AnalyticsData {
  revenue: {
    total: number
    thisMonth: number
    lastMonth: number
    growth: number
  }
  projects: {
    total: number
    completed: number
    inProgress: number
    completionRate: number
  }
  bids: {
    total: number
    accepted: number
    pending: number
    winRate: number
  }
  rating: {
    average: number
    totalReviews: number
    distribution: { [key: number]: number }
  }
  monthlyRevenue: { month: string; amount: number }[]
  topCategories: { category: string; count: number; revenue: number }[]
}

function ProAnalyticsPage() {
  const { } = useUser()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState("6months")

  useEffect(() => {
    loadAnalytics()
  }, [timeRange])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Simulate API call - in real app would call analytics service
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setAnalytics(getMockAnalytics())
    } catch (err) {
      setError("Failed to load analytics. Please try again.")
      console.error("Error loading analytics:", err)
    } finally {
      setLoading(false)
    }
  }

  const getMockAnalytics = (): AnalyticsData => ({
    revenue: {
      total: 125000,
      thisMonth: 18500,
      lastMonth: 15200,
      growth: 21.7
    },
    projects: {
      total: 24,
      completed: 18,
      inProgress: 6,
      completionRate: 75
    },
    bids: {
      total: 45,
      accepted: 19,
      pending: 8,
      winRate: 42.2
    },
    rating: {
      average: 4.8,
      totalReviews: 32,
      distribution: { 5: 24, 4: 6, 3: 2, 2: 0, 1: 0 }
    },
    monthlyRevenue: [
      { month: "Jan", amount: 12000 },
      { month: "Feb", amount: 15000 },
      { month: "Mar", amount: 18000 },
      { month: "Apr", amount: 16500 },
      { month: "May", amount: 20000 },
      { month: "Jun", amount: 18500 }
    ],
    topCategories: [
      { category: "Kitchen", count: 8, revenue: 65000 },
      { category: "Bathroom", count: 6, revenue: 35000 },
      { category: "Flooring", count: 4, revenue: 18000 },
      { category: "Painting", count: 3, revenue: 7000 }
    ]
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
          <UnifiedNavigation />
          <div className="container mx-auto px-6 py-8">
            <PageHeader
              title="Analytics"
              description="Track your business performance and growth"
              breadcrumbs={[
                { label: 'Home', href: '/' },
                { label: 'Pro Dashboard', href: '/pro/dashboard' },
                { label: 'Analytics', isActive: true }
              ]}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[1, 2, 3, 4].map((i) => (
                <EnhancedCard key={i} loading={true} />
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[1, 2].map((i) => (
                <EnhancedCard key={i} loading={true} className="h-64" />
              ))}
            </div>
          </div>
        </div>
      </>
    )
  }

  if (error || !analytics) {
    return (
      <>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
          <UnifiedNavigation />
          <div className="container mx-auto px-6 py-8">
            <PageHeader
              title="Analytics"
              description="Track your business performance and growth"
              breadcrumbs={[
                { label: 'Home', href: '/' },
                { label: 'Pro Dashboard', href: '/pro/dashboard' },
                { label: 'Analytics', isActive: true }
              ]}
            />
            <EnhancedCard className="p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">Error Loading Analytics</h3>
              <p className="text-slate-500 mb-4">{error}</p>
              <Button onClick={loadAnalytics}>
                <Loader2 className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </EnhancedCard>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />

        <div className="container mx-auto px-6 py-8">
          <PageHeader
            title="Analytics"
            description="Track your business performance and growth"
            breadcrumbs={[
              { label: 'Home', href: '/' },
              { label: 'Pro Dashboard', href: '/pro/dashboard' },
              { label: 'Analytics', isActive: true }
            ]}
            actions={
              <Tabs value={timeRange} onValueChange={setTimeRange}>
                <TabsList>
                  <TabsTrigger value="3months">3M</TabsTrigger>
                  <TabsTrigger value="6months">6M</TabsTrigger>
                  <TabsTrigger value="1year">1Y</TabsTrigger>
                </TabsList>
              </Tabs>
            }
          />

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className={`flex items-center space-x-1 text-sm ${analytics.revenue.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.revenue.growth > 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                <span>{formatPercentage(analytics.revenue.growth)}</span>
              </div>
            </div>
            <div>
              <p className="text-sm text-slate-500 mb-1">Total Revenue</p>
              <p className="text-2xl font-semibold text-slate-900">{formatCurrency(analytics.revenue.total)}</p>
              <p className="text-xs text-slate-500 mt-1">
                {formatCurrency(analytics.revenue.thisMonth)} this month
              </p>
            </div>
          </EnhancedCard>

          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-sm text-slate-500">
                {analytics.projects.completionRate}% rate
              </div>
            </div>
            <div>
              <p className="text-sm text-slate-500 mb-1">Projects Completed</p>
              <p className="text-2xl font-semibold text-slate-900">{analytics.projects.completed}</p>
              <p className="text-xs text-slate-500 mt-1">
                {analytics.projects.inProgress} in progress
              </p>
            </div>
          </EnhancedCard>

          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-sm text-slate-500">
                {analytics.bids.winRate}% win rate
              </div>
            </div>
            <div>
              <p className="text-sm text-slate-500 mb-1">Bids Accepted</p>
              <p className="text-2xl font-semibold text-slate-900">{analytics.bids.accepted}</p>
              <p className="text-xs text-slate-500 mt-1">
                {analytics.bids.pending} pending
              </p>
            </div>
          </EnhancedCard>

          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="text-sm text-slate-500">
                {analytics.rating.totalReviews} reviews
              </div>
            </div>
            <div>
              <p className="text-sm text-slate-500 mb-1">Average Rating</p>
              <p className="text-2xl font-semibold text-slate-900">{analytics.rating.average}</p>
              <div className="flex items-center mt-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-3 w-3 ${
                      star <= Math.floor(analytics.rating.average)
                        ? 'text-yellow-400 fill-current'
                        : 'text-slate-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </EnhancedCard>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Revenue Chart */}
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-slate-900">Monthly Revenue</h3>
              <Activity className="h-5 w-5 text-slate-400" />
            </div>
            <div className="space-y-4">
              {analytics.monthlyRevenue.map((item) => (
                <div key={item.month} className="flex items-center justify-between">
                  <span className="text-sm text-slate-500">{item.month}</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-32 bg-slate-100 rounded-full h-2">
                      <div
                        className="bg-brand-secondary h-2 rounded-full"
                        style={{
                          width: `${(item.amount / Math.max(...analytics.monthlyRevenue.map(r => r.amount))) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-slate-900 w-16 text-right">
                      {formatCurrency(item.amount)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </EnhancedCard>

          {/* Top Categories */}
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-slate-900">Top Categories</h3>
              <PieChart className="h-5 w-5 text-slate-400" />
            </div>
            <div className="space-y-4">
              {analytics.topCategories.map((category, index) => (
                <div key={category.category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      index === 0 ? 'bg-brand-primary' :
                      index === 1 ? 'bg-brand-secondary' :
                      index === 2 ? 'bg-brand-accent' : 'bg-slate-300'
                    }`} />
                    <span className="text-sm text-slate-900">{category.category}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-slate-900">
                      {formatCurrency(category.revenue)}
                    </div>
                    <div className="text-xs text-slate-500">
                      {category.count} projects
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </EnhancedCard>
        </div>

        {/* Recent Activity */}
        <EnhancedCard className="p-6">
          <h3 className="text-lg font-medium text-slate-900 mb-6">Recent Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 p-4 bg-slate-50 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div className="flex-1">
                <p className="text-sm font-medium text-slate-900">Project completed</p>
                <p className="text-xs text-slate-500">Kitchen Remodel for John Smith - $14,500</p>
              </div>
              <span className="text-xs text-slate-500">2 hours ago</span>
            </div>
            
            <div className="flex items-center space-x-4 p-4 bg-slate-50 rounded-lg">
              <DollarSign className="h-5 w-5 text-blue-500" />
              <div className="flex-1">
                <p className="text-sm font-medium text-slate-900">Payment received</p>
                <p className="text-xs text-slate-500">Final payment for Bathroom Renovation - $9,800</p>
              </div>
              <span className="text-xs text-slate-500">1 day ago</span>
            </div>
            
            <div className="flex items-center space-x-4 p-4 bg-slate-50 rounded-lg">
              <Star className="h-5 w-5 text-yellow-500" />
              <div className="flex-1">
                <p className="text-sm font-medium text-slate-900">New review received</p>
                <p className="text-xs text-slate-500">5-star review from Sarah Johnson</p>
              </div>
              <span className="text-xs text-slate-500">3 days ago</span>
            </div>
          </div>
        </EnhancedCard>
        </div>
      </div>
    </>
  )
}

export default function ProtectedProAnalyticsPage() {
  return (
    <ProRoute>
      <ProAnalyticsPage />
    </ProRoute>
  )
}
