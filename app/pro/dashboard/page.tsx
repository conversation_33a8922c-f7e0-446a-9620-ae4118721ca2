"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { ProRoute } from "@/components/route-guard"

import { useUser } from "@/contexts/user-context"
import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Clock,
  CheckCircle,
  MessageCircle,
  Star,
  ArrowRight,
  Calendar,
  DollarSign,
  Users,
  BarChart4,
  Briefcase,
  Hammer,
  TrendingUp,
} from "lucide-react"
import Link from "next/link"

export default function ProDashboardPage() {
  const [activeTab, setActiveTab] = useState("active")
  const { user } = useUser()

  const projects = [
    {
      id: "1",
      title: "Kitchen Remodel",
      client: "<PERSON>",
      status: "in-progress",
      progress: 65,
      nextMilestone: "Cabinet Installation",
      dueDate: "Dec 15",
      budget: "$15,000",
      location: "San Francisco, CA",
    },
    {
      id: "2",
      title: "Bathroom Renovation",
      client: "<PERSON>",
      status: "bidding",
      budget: "$8,000",
      location: "Oakland, CA",
      bidSubmitted: true,
      bidAmount: "$7,500",
      bidDate: "3 days ago",
    },
    {
      id: "3",
      title: "Flooring Installation",
      client: "<PERSON>",
      status: "scheduled",
      startDate: "Dec 20",
      budget: "$5,000",
      location: "Berkeley, CA",
    },
  ]

  const filteredProjects = projects.filter((project) => {
    if (activeTab === "active") return project.status === "in-progress"
    if (activeTab === "bidding") return project.status === "bidding"
    if (activeTab === "scheduled") return project.status === "scheduled"
    return true
  })

  const stats = [
    { label: "Active Projects", value: "3", icon: Hammer, color: "text-brand-primary" },
    { label: "This Month", value: "$12.5K", icon: DollarSign, color: "text-brand-secondary" },
    { label: "Avg Rating", value: "4.8", icon: Star, color: "text-brand-accent" },
    { label: "Completion Rate", value: "97%", icon: TrendingUp, color: "text-status-success" },
  ]

  const recentActivity = [
    {
      id: "1",
      type: "milestone",
      message: "Completed 'Plumbing & Electrical' milestone for Kitchen Remodel",
      time: "2 hours ago",
      project: "Kitchen Remodel",
    },
    {
      id: "2",
      type: "message",
      message: "New message from John Smith about Kitchen Remodel",
      time: "5 hours ago",
      project: "Kitchen Remodel",
    },
    {
      id: "3",
      type: "bid",
      message: "Your bid for Bathroom Renovation was viewed by Sarah Johnson",
      time: "1 day ago",
      project: "Bathroom Renovation",
    },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "in-progress":
        return <Clock className="h-4 w-4 text-status-info" />
      case "bidding":
        return <DollarSign className="h-4 w-4 text-status-warning" />
      case "scheduled":
        return <Calendar className="h-4 w-4 text-status-success" />
      default:
        return <Clock className="h-4 w-4 text-slate-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "in-progress":
        return "In Progress"
      case "bidding":
        return "Bidding"
      case "scheduled":
        return "Scheduled"
      default:
        return status
    }
  }

  return (
    <ProRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
        <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-12 gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-slate-900 mb-2">
              Welcome back, {user?.name || "Professional"}
            </h1>
            <p className="text-lg text-slate-600">Manage your projects, bids, and business performance</p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="card-premium p-6 hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-2">{stat.label}</p>
                  <p className="text-3xl font-semibold text-slate-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Projects */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-semibold text-slate-900">Projects</h2>
              <Link href="/pro/projects">
                <Button variant="outline" size="sm" className="text-slate-600 hover:text-slate-900 border-slate-200 hover:border-slate-300">
                  View All
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>

            <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab} className="mb-8">
              <TabsList className="bg-white border border-slate-200 p-1 rounded-xl shadow-sm">
                <TabsTrigger
                  value="active"
                  className="rounded-lg data-[state=active]:bg-slate-900 data-[state=active]:text-white data-[state=active]:shadow-sm font-medium"
                >
                  Active
                </TabsTrigger>
                <TabsTrigger
                  value="bidding"
                  className="rounded-lg data-[state=active]:bg-slate-900 data-[state=active]:text-white data-[state=active]:shadow-sm font-medium"
                >
                  Bidding
                </TabsTrigger>
                <TabsTrigger
                  value="scheduled"
                  className="rounded-lg data-[state=active]:bg-slate-900 data-[state=active]:text-white data-[state=active]:shadow-sm font-medium"
                >
                  Scheduled
                </TabsTrigger>
                <TabsTrigger
                  value="all"
                  className="rounded-lg data-[state=active]:bg-slate-900 data-[state=active]:text-white data-[state=active]:shadow-sm font-medium"
                >
                  All
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="space-y-6">
              {filteredProjects.length === 0 ? (
                <div className="card-premium p-12 text-center">
                  <Briefcase className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">No projects found</h3>
                  <p className="text-slate-600">You don't have any projects in this category</p>
                </div>
              ) : (
                filteredProjects.map((project) => (
                  <div
                    key={project.id}
                    className="card-premium p-6 hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-medium text-slate-900 mb-1">{project.title}</h3>
                        <div className="flex items-center space-x-4 text-sm text-slate-500">
                          <span className="flex items-center">
                            <div
                              className={`w-2 h-2 rounded-full mr-2 ${
                                project.status === "in-progress"
                                  ? "bg-blue-500"
                                  : project.status === "bidding"
                                    ? "bg-yellow-500"
                                    : "bg-green-500"
                              }`}
                            />
                            {getStatusText(project.status)}
                          </span>
                          <span>Client: {project.client}</span>
                          <span>{project.location}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-slate-900">{project.budget}</div>
                        {project.bidAmount && (
                          <div className="text-xs text-slate-500">Your bid: {project.bidAmount}</div>
                        )}
                      </div>
                    </div>

                    {project.status === "in-progress" && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-slate-600">Progress</span>
                          <span className="text-sm font-medium text-slate-900">{project.progress}%</span>
                        </div>
                        <div className="w-full bg-slate-100 rounded-full h-2">
                          <div
                            className="bg-slate-900 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                        {project.nextMilestone && (
                          <div className="flex items-center justify-between mt-3 text-sm">
                            <span className="text-slate-500">Next: {project.nextMilestone}</span>
                            <span className="text-slate-500">Due {project.dueDate}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {project.status === "scheduled" && (
                      <div className="mb-4 text-sm">
                        <div className="flex items-center text-slate-600">
                          <Calendar className="h-4 w-4 mr-2 text-green-600" />
                          <span>Starts on {project.startDate}</span>
                        </div>
                      </div>
                    )}

                    <div className="flex space-x-2">
                      <Link href={`/pro/projects/${project.id}`}>
                        <Button size="action" className="bg-slate-900 hover:bg-slate-800 text-white">
                          View Details
                        </Button>
                      </Link>
                      <Button
                        size="action"
                        variant="outline"
                        className="bg-transparent border-slate-200 hover:border-slate-300"
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Message
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Recent Activity */}
            <div className="card-premium p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-6">Recent Activity</h3>

              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div
                      className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                        activity.type === "milestone"
                          ? "bg-green-500"
                          : activity.type === "bid"
                            ? "bg-blue-500"
                            : "bg-purple-500"
                      }`}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-slate-900 leading-relaxed">{activity.message}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-slate-500">{activity.time}</span>
                        <span className="text-xs text-slate-400">•</span>
                        <span className="text-xs text-slate-500">{activity.project}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Button variant="ghost" size="sm" className="w-full mt-6 text-slate-500 hover:text-slate-700">
                View All Activity
              </Button>
            </div>

            {/* Performance */}
            <div className="card-premium p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-slate-900">Performance</h3>
                <Link href="/pro/analytics">
                  <Button variant="ghost" size="sm" className="text-slate-500 hover:text-slate-700 p-1">
                    <BarChart4 className="h-4 w-4" />
                  </Button>
                </Link>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-slate-500">Bid Win Rate</span>
                    <span className="text-sm font-medium text-slate-900">68%</span>
                  </div>
                  <div className="w-full bg-slate-100 rounded-full h-1.5">
                    <div className="bg-green-500 h-1.5 rounded-full" style={{ width: "68%" }} />
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-slate-500">On-time Completion</span>
                    <span className="text-sm font-medium text-slate-900">95%</span>
                  </div>
                  <div className="w-full bg-slate-100 rounded-full h-1.5">
                    <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: "95%" }} />
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-slate-500">Client Satisfaction</span>
                    <span className="text-sm font-medium text-slate-900">4.8/5</span>
                  </div>
                  <div className="w-full bg-slate-100 rounded-full h-1.5">
                    <div className="bg-yellow-500 h-1.5 rounded-full" style={{ width: "96%" }} />
                  </div>
                </div>
              </div>
            </div>

            {/* Upcoming */}
            <div className="card-premium p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-6">Upcoming</h3>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-slate-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-900">Cabinet Installation</p>
                    <p className="text-xs text-slate-500">Tomorrow, 9:00 AM</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                    <Users className="h-4 w-4 text-slate-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-900">Client Meeting</p>
                    <p className="text-xs text-slate-500">Dec 18, 2:00 PM</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-4 w-4 text-slate-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-900">Payment Due</p>
                    <p className="text-xs text-slate-500">Dec 20, $4,000</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      </div>
    </ProRoute>
  )
}
