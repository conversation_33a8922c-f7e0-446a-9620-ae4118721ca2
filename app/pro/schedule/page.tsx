"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { PageHeader } from "@/components/breadcrumb"
import { ProRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar,
  Clock, 
  MapPin,
  User,
  Plus,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  Edit,
  Trash2
} from "lucide-react"

interface ScheduleEvent {
  id: string
  title: string
  type: "project" | "consultation" | "estimate" | "meeting"
  client: string
  location: string
  startTime: Date
  endTime: Date
  status: "scheduled" | "in-progress" | "completed" | "cancelled"
  notes?: string
}

export default function ProSchedulePage() {
  const { user } = useUser()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<"week" | "month">("week")
  const [events] = useState<ScheduleEvent[]>([
    {
      id: "1",
      title: "Kitchen Consultation",
      type: "consultation",
      client: "<PERSON>",
      location: "123 Main St, San Francisco",
      startTime: new Date(2024, 11, 15, 9, 0),
      endTime: new Date(2024, 11, 15, 10, 30),
      status: "scheduled",
      notes: "Initial consultation for kitchen remodel"
    },
    {
      id: "2",
      title: "Bathroom Installation",
      type: "project",
      client: "Sarah Johnson",
      location: "456 Oak Ave, Oakland",
      startTime: new Date(2024, 11, 15, 14, 0),
      endTime: new Date(2024, 11, 15, 17, 0),
      status: "in-progress",
      notes: "Day 3 of bathroom renovation"
    },
    {
      id: "3",
      title: "Deck Estimate",
      type: "estimate",
      client: "Mike Brown",
      location: "789 Pine St, Berkeley",
      startTime: new Date(2024, 11, 16, 11, 0),
      endTime: new Date(2024, 11, 16, 12, 0),
      status: "scheduled",
      notes: "Estimate for composite deck construction"
    }
  ])

  const getEventTypeColor = (type: ScheduleEvent['type']) => {
    switch (type) {
      case "project":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "consultation":
        return "bg-green-100 text-green-800 border-green-200"
      case "estimate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "meeting":
        return "bg-purple-100 text-purple-800 border-purple-200"
      default:
        return "bg-slate-100 text-slate-800 border-slate-200"
    }
  }

  const getStatusColor = (status: ScheduleEvent['status']) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-50 text-blue-700 border-blue-200"
      case "in-progress":
        return "bg-green-50 text-green-700 border-green-200"
      case "completed":
        return "bg-slate-50 text-slate-700 border-slate-200"
      case "cancelled":
        return "bg-red-50 text-red-700 border-red-200"
      default:
        return "bg-slate-50 text-slate-700 border-slate-200"
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    })
  }

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (viewMode === 'week') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7))
    } else {
      newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1))
    }
    setCurrentDate(newDate)
  }

  const todaysEvents = events.filter(event => {
    const today = new Date()
    const eventDate = event.startTime
    return eventDate.toDateString() === today.toDateString()
  })

  const upcomingEvents = events.filter(event => {
    const today = new Date()
    const eventDate = event.startTime
    return eventDate > today
  }).slice(0, 5)

  return (
    <ProRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />

        <div className="container mx-auto px-6 py-8">
          <PageHeader
            title="Schedule"
            description="Manage your appointments, consultations, and project timelines"
            actions={
              <div className="flex items-center space-x-3">
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Event
                </Button>
              </div>
            }
          />

        {/* View Controls */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate('prev')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <h2 className="text-xl font-medium text-slate-900">
              {currentDate.toLocaleDateString('en-US', { 
                month: 'long', 
                year: 'numeric' 
              })}
            </h2>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate('next')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "week" | "month")}>
            <TabsList>
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="month">Month</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Calendar/Schedule View */}
          <div className="lg:col-span-2">
            <EnhancedCard className="p-6">
              <h3 className="text-lg font-medium text-slate-900 mb-6">
                {viewMode === 'week' ? 'This Week' : 'This Month'}
              </h3>
              
              <div className="space-y-4">
                {events.map((event) => (
                  <div
                    key={event.id}
                    className="flex items-start space-x-4 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-3 h-3 rounded-full bg-brand-secondary mt-2"></div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium text-slate-900">{event.title}</h4>
                          <p className="text-sm text-slate-600 mt-1">{event.client}</p>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Badge className={getEventTypeColor(event.type)}>
                            {event.type}
                          </Badge>
                          <Badge variant="outline" className={getStatusColor(event.status)}>
                            {event.status}
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 mt-3 text-sm text-slate-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(event.startTime)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{formatTime(event.startTime)} - {formatTime(event.endTime)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span className="truncate">{event.location}</span>
                        </div>
                      </div>
                      
                      {event.notes && (
                        <p className="text-sm text-slate-600 mt-2">{event.notes}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </EnhancedCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Today's Schedule */}
            <EnhancedCard className="p-6">
              <h3 className="text-lg font-medium text-slate-900 mb-4">Today's Schedule</h3>
              
              {todaysEvents.length === 0 ? (
                <p className="text-slate-500 text-center py-4">No appointments today</p>
              ) : (
                <div className="space-y-3">
                  {todaysEvents.map((event) => (
                    <div key={event.id} className="flex items-center space-x-3 p-3 bg-slate-50 rounded-lg">
                      <div className="w-2 h-2 rounded-full bg-brand-secondary"></div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-slate-900 truncate">{event.title}</p>
                        <p className="text-sm text-slate-500">{formatTime(event.startTime)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </EnhancedCard>

            {/* Upcoming Events */}
            <EnhancedCard className="p-6">
              <h3 className="text-lg font-medium text-slate-900 mb-4">Upcoming</h3>
              
              <div className="space-y-3">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center space-x-3 p-3 border border-slate-200 rounded-lg">
                    <div className="flex-shrink-0">
                      <User className="h-4 w-4 text-slate-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-slate-900 truncate">{event.title}</p>
                      <p className="text-sm text-slate-500">{event.client}</p>
                      <p className="text-xs text-slate-400">
                        {event.startTime.toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric' 
                        })} at {formatTime(event.startTime)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </EnhancedCard>

            {/* Quick Actions */}
            <EnhancedCard className="p-6">
              <h3 className="text-lg font-medium text-slate-900 mb-4">Quick Actions</h3>
              
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Consultation
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  Block Time Off
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="h-4 w-4 mr-2" />
                  Update Availability
                </Button>
              </div>
            </EnhancedCard>
          </div>
        </div>
        </div>
      </div>
    </ProRoute>
  )
}
