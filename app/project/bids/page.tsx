"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { useUser } from "@/contexts/user-context"
import { Star, MessageCircle, Check, Heart, X, ArrowRight } from "lucide-react"
import { RatingDisplay } from "@/components/ui/interactive-elements"

interface Bid {
  id: string
  contractorName: string
  rating: number
  reviewCount: number
  price: string
  timeline: string
  description: string
  verified: boolean
  responseTime: string
  phone?: string
  email?: string
}

export default function BidsPage() {
  const { user } = useUser()
  const router = useRouter()
  const [bids, setBids] = useState<Bid[]>([])
  const [selectedBids, setSelectedBids] = useState<string[]>([])
  const [rejectedBids, setRejectedBids] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setTimeout(() => {
      setBids([
        {
          id: "1",
          contractorName: "Mike's Kitchen Experts",
          rating: 4.9,
          reviewCount: 127,
          price: "$12,500",
          timeline: "2-3 weeks",
          description: "Complete kitchen remodel with premium materials. 15 years experience with similar projects.",
          verified: true,
          responseTime: "< 2 hours",
          phone: "(*************",
          email: "<EMAIL>",
        },
        {
          id: "2",
          contractorName: "Premium Home Solutions",
          rating: 4.8,
          reviewCount: 89,
          price: "$11,000",
          timeline: "3-4 weeks",
          description: "Full-service renovation with custom cabinetry. Licensed and insured.",
          verified: true,
          responseTime: "< 4 hours",
          phone: "(*************",
          email: "<EMAIL>",
        },
        {
          id: "3",
          contractorName: "Affordable Kitchen Co.",
          rating: 4.6,
          reviewCount: 203,
          price: "$9,500",
          timeline: "4-5 weeks",
          description: "Quality renovation at competitive prices. Local family business.",
          verified: false,
          responseTime: "< 1 day",
          phone: "(*************",
          email: "<EMAIL>",
        },
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const handleBidAction = (bidId: string, action: "select" | "reject") => {
    if (action === "select") {
      setSelectedBids((prev) => [...prev, bidId])
      setRejectedBids((prev) => prev.filter((id) => id !== bidId))
    } else {
      setRejectedBids((prev) => [...prev, bidId])
      setSelectedBids((prev) => prev.filter((id) => id !== bidId))
    }
  }

  const handleContinueWithSelected = () => {
    if (selectedBids.length === 0) return

    // Store selected bids in localStorage for the next step
    localStorage.setItem('selectedBids', JSON.stringify(selectedBids))
    localStorage.setItem('selectedBidsData', JSON.stringify(
      bids.filter(bid => selectedBids.includes(bid.id))
    ))

    // Navigate to contractor comparison or contract negotiation page
    router.push('/project/compare-contractors')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <UnifiedNavigation />
        <div className="container-premium section-premium">
          <div className="text-center py-20">
            <div className="animate-pulse text-slate-500 text-lg">Finding contractors...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        <div className="mb-12">
          <h1 className="text-3xl font-semibold text-slate-900 mb-3">Your Bids</h1>
          <p className="text-lg text-slate-600">{bids.length} contractors are interested in your project</p>
        </div>

        <div className="space-y-6">
          {bids.map((bid) => (
            <div
              key={bid.id}
              className={`card-premium p-6 transition-all duration-200 ${
                selectedBids.includes(bid.id)
                  ? "ring-2 ring-emerald-500 bg-emerald-50/50"
                  : rejectedBids.includes(bid.id)
                    ? "ring-2 ring-red-500 bg-red-50/50"
                    : "hover:shadow-lg"
              }`}
            >
              <div className="flex items-start justify-between mb-6">
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-slate-900">{bid.contractorName}</h3>
                    {bid.verified && (
                      <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                        <Check className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-3 text-sm text-slate-500">
                    <RatingDisplay
                      rating={bid.rating}
                      reviewCount={bid.reviewCount}
                      size="sm"
                    />
                    <span>•</span>
                    <span>Responded {bid.responseTime}</span>
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-xl font-light text-slate-900">{bid.price}</div>
                  <div className="text-sm text-slate-500">{bid.timeline}</div>
                </div>
              </div>

              <p className="text-slate-600 mb-6 leading-relaxed">{bid.description}</p>

              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBidAction(bid.id, "reject")}
                  className={`minimal-button border-slate-200 hover:border-red-300 bg-transparent ${
                    rejectedBids.includes(bid.id) ? "border-red-300 text-red-600" : ""
                  }`}
                >
                  Pass
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="minimal-button border-slate-200 hover:border-slate-300 bg-transparent"
                >
                  Message
                </Button>

                <Button
                  onClick={() => handleBidAction(bid.id, "select")}
                  className={`minimal-button ${
                    selectedBids.includes(bid.id)
                      ? "bg-green-600 hover:bg-green-700 text-white"
                      : "bg-slate-900 hover:bg-slate-800 text-white"
                  }`}
                  size="sm"
                >
                  {selectedBids.includes(bid.id) ? (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Selected
                    </>
                  ) : (
                    <>
                      <Heart className="h-4 w-4 mr-2" />
                      Interested
                    </>
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>

        {selectedBids.length > 0 && (
          <div className="fixed bottom-6 left-6 right-6 bg-white border border-slate-200 rounded-xl p-4 shadow-lg fade-in">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              <div>
                <div className="font-medium text-slate-900">
                  {selectedBids.length} contractor{selectedBids.length > 1 ? "s" : ""} selected
                </div>
                <div className="text-sm text-slate-500">Ready to move forward?</div>
              </div>
              <Button
                onClick={handleContinueWithSelected}
                className="bg-slate-900 hover:bg-slate-800 text-white font-semibold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 flex items-center space-x-2"
              >
                <span>Continue with Selected</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
