"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { Star, MapPin, Clock, CheckCircle, ArrowLeft, Camera } from "lucide-react"
import Link from "next/link"
import { BookmarkButton } from "@/components/ui/interactive-elements"

interface ProjectData {
  description: string
  category: string
  budget: string
  timeline: string
  location: string
  photos: File[]
}

interface Contractor {
  id: string
  name: string
  specialty: string
  rating: number
  reviewCount: number
  location: string
  priceRange: string
  verified: boolean
  responseTime: string
  completedProjects: number
  image: string
}

export default function ProjectContractorsPage() {
  const { user } = useUser()
  const router = useRouter()
  const [projectData, setProjectData] = useState<ProjectData | null>(null)
  const [selectedContractors, setSelectedContractors] = useState<string[]>([])

  // Mock contractors data
  const contractors: Contractor[] = [
    {
      id: "1",
      name: "Elite Kitchen Renovations",
      specialty: "Kitchen Remodel",
      rating: 4.9,
      reviewCount: 127,
      location: "San Francisco, CA",
      priceRange: "$10K - $50K",
      verified: true,
      responseTime: "< 2 hours",
      completedProjects: 89,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "2",
      name: "Premium Home Solutions",
      specialty: "Full Home Renovation",
      rating: 4.8,
      reviewCount: 89,
      location: "Oakland, CA",
      priceRange: "$15K - $100K",
      verified: true,
      responseTime: "< 4 hours",
      completedProjects: 156,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "3",
      name: "Modern Bath Designs",
      specialty: "Bathroom Renovation",
      rating: 4.7,
      reviewCount: 203,
      location: "Berkeley, CA",
      priceRange: "$5K - $25K",
      verified: true,
      responseTime: "< 6 hours",
      completedProjects: 78,
      image: "/placeholder.svg?height=200&width=300",
    }
  ]

  useEffect(() => {
    // Load project data from localStorage if available
    const pendingProject = localStorage.getItem('pendingProject')
    if (pendingProject) {
      setProjectData(JSON.parse(pendingProject))
      localStorage.removeItem('pendingProject')
    }
  }, [])

  const handleContractorSelect = (contractorId: string) => {
    setSelectedContractors(prev => 
      prev.includes(contractorId) 
        ? prev.filter(id => id !== contractorId)
        : [...prev, contractorId]
    )
  }

  const handleSubmitProject = () => {
    if (selectedContractors.length === 0) {
      alert("Please select at least one contractor to receive quotes from.")
      return
    }
    
    // Here you would typically submit the project data and selected contractors
    console.log("Project Data:", projectData)
    console.log("Selected Contractors:", selectedContractors)
    
    router.push("/dashboard")
  }

  return (
    <CustomerRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />
        
        <div className="container-premium section-premium">
          {/* Header */}
          <div className="mb-12">
            <div className="flex items-center space-x-4 mb-6">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back</span>
              </Button>
            </div>
            
            <h1 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              Choose Your Contractors
            </h1>
            <p className="text-lg text-slate-600 max-w-3xl">
              Select contractors you'd like to receive quotes from. We'll send your project details to them and they'll respond with personalized proposals.
            </p>
          </div>

          {/* Project Summary */}
          {projectData && (
            <div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
              <h2 className="text-xl font-bold text-slate-900 mb-4">Your Project Summary</h2>

              {/* Project Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div>
                  <div className="text-sm font-medium text-slate-500">Category</div>
                  <div className="text-slate-900">{projectData.category || 'Not specified'}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-slate-500">Budget</div>
                  <div className="text-slate-900">{projectData.budget || 'Not specified'}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-slate-500">Timeline</div>
                  <div className="text-slate-900">{projectData.timeline || 'Not specified'}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-slate-500">Location</div>
                  <div className="text-slate-900">{projectData.location || 'Not specified'}</div>
                </div>
              </div>

              {/* Project Description */}
              {projectData.description && (
                <div className="mb-6">
                  <div className="text-sm font-medium text-slate-500 mb-2">Project Description</div>
                  <div className="text-slate-900 bg-slate-50 p-4 rounded-xl">
                    {projectData.description}
                  </div>
                </div>
              )}

              {/* Project Images */}
              {projectData.photos && projectData.photos.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-slate-500 mb-3">
                    Project Images ({projectData.photos.length})
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {projectData.photos.map((photo, index) => (
                      <ContractorImageThumbnail key={index} file={photo} index={index} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Contractors Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
            {contractors.map((contractor) => (
              <div
                key={contractor.id}
                className={`relative bg-white rounded-2xl p-6 shadow-lg transition-all duration-200 cursor-pointer ${
                  selectedContractors.includes(contractor.id)
                    ? 'ring-2 ring-blue-500 bg-blue-50'
                    : 'hover:shadow-xl hover:scale-105'
                }`}
                onClick={() => handleContractorSelect(contractor.id)}
              >
                <div className="absolute top-2 right-2 z-10" onClick={(e) => e.stopPropagation()}>
                  <BookmarkButton
                    itemId={contractor.id}
                    itemType="contractor"
                    className="w-6 h-6 p-0 bg-white/90 hover:bg-white rounded-full shadow-sm"
                  />
                </div>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-bold text-slate-900">{contractor.name}</h3>
                      {contractor.verified && (
                        <CheckCircle className="h-5 w-5 text-emerald-500" />
                      )}
                    </div>
                    <p className="text-slate-600 text-sm">{contractor.specialty}</p>
                  </div>
                  
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                    selectedContractors.includes(contractor.id)
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-slate-300'
                  }`}>
                    {selectedContractors.includes(contractor.id) && (
                      <CheckCircle className="h-4 w-4 text-white" />
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="font-medium">{contractor.rating}</span>
                      <span className="text-slate-500">({contractor.reviewCount})</span>
                    </div>
                    <div className="flex items-center space-x-1 text-slate-600">
                      <MapPin className="h-4 w-4" />
                      <span>{contractor.location}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-1 text-slate-600">
                      <Clock className="h-4 w-4" />
                      <span>Responds {contractor.responseTime}</span>
                    </div>
                    <div className="font-medium text-slate-900">
                      {contractor.priceRange}
                    </div>
                  </div>

                  <div className="text-sm text-slate-600">
                    {contractor.completedProjects} completed projects
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Submit Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleSubmitProject}
              disabled={selectedContractors.length === 0}
              className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white px-8 py-3 text-lg"
            >
              Request Quotes from {selectedContractors.length} Contractor{selectedContractors.length !== 1 ? 's' : ''}
            </Button>
          </div>
        </div>
      </div>
    </CustomerRoute>
  )
}

// Component for displaying image thumbnails in the contractor selection page
function ContractorImageThumbnail({ file, index }: { file: File; index: number }) {
  const [preview, setPreview] = useState<string | null>(null)

  useEffect(() => {
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  })

  return (
    <div className="relative aspect-square bg-slate-100 rounded-lg overflow-hidden group cursor-pointer hover:shadow-md transition-all duration-200">
      {preview ? (
        <img
          src={preview}
          alt={`Project image ${index + 1}`}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <Camera className="h-4 w-4 text-slate-400" />
        </div>
      )}
      <div className="absolute bottom-1 left-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
        {index + 1}
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200" />
    </div>
  )
}
