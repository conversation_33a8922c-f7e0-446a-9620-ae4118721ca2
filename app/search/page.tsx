"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>lider } from "@/components/ui/slider"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { SearchIcon, Filter, MapPin, Star, CheckCircle, X, SlidersHorizontal } from "lucide-react"
import Link from "next/link"

interface SearchFilters {
  query: string
  category: string
  priceRange: [number, number]
  distance: number
  rating: number
  verified: boolean
}

interface Contractor {
  id: string
  name: string
  specialty: string
  rating: number
  reviewCount: number
  location: string
  distance: number
  price: number
  priceLabel: string
  verified: boolean
  image: string
}

export default function SearchPage() {
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState<SearchFilters>({
    query: "",
    category: "all",
    priceRange: [0, 100000],
    distance: 25,
    rating: 0,
    verified: false,
  })
  const [results, setResults] = useState<Contractor[]>([])

  const categories = [
    { id: "all", label: "All Categories" },
    { id: "kitchen", label: "Kitchen Remodeling" },
    { id: "bathroom", label: "Bathroom Renovation" },
    { id: "flooring", label: "Flooring" },
    { id: "painting", label: "Painting" },
    { id: "electrical", label: "Electrical" },
    { id: "plumbing", label: "Plumbing" },
    { id: "landscaping", label: "Landscaping" },
  ]

  // Mock data for contractors
  const allContractors: Contractor[] = [
    {
      id: "1",
      name: "Mike's Kitchen Experts",
      specialty: "Kitchen Remodeling",
      rating: 4.9,
      reviewCount: 127,
      location: "San Francisco, CA",
      distance: 3.2,
      price: 15000,
      priceLabel: "$$$",
      verified: true,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "2",
      name: "Premium Home Solutions",
      specialty: "Bathroom Renovation",
      rating: 4.8,
      reviewCount: 89,
      location: "Oakland, CA",
      distance: 8.7,
      price: 12000,
      priceLabel: "$$$",
      verified: true,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "3",
      name: "Bay Area Flooring Co.",
      specialty: "Flooring",
      rating: 4.7,
      reviewCount: 203,
      location: "San Jose, CA",
      distance: 15.3,
      price: 8000,
      priceLabel: "$$",
      verified: true,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "4",
      name: "Precision Painters",
      specialty: "Painting",
      rating: 4.6,
      reviewCount: 156,
      location: "Berkeley, CA",
      distance: 12.1,
      price: 5000,
      priceLabel: "$$",
      verified: false,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "5",
      name: "Elite Electrical Services",
      specialty: "Electrical",
      rating: 4.8,
      reviewCount: 78,
      location: "San Francisco, CA",
      distance: 4.5,
      price: 3000,
      priceLabel: "$",
      verified: true,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "6",
      name: "Modern Plumbing Solutions",
      specialty: "Plumbing",
      rating: 4.5,
      reviewCount: 112,
      location: "Daly City, CA",
      distance: 9.8,
      price: 2500,
      priceLabel: "$",
      verified: false,
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: "7",
      name: "Green Landscapes",
      specialty: "Landscaping",
      rating: 4.7,
      reviewCount: 95,
      location: "Marin, CA",
      distance: 18.6,
      price: 7500,
      priceLabel: "$$",
      verified: true,
      image: "/placeholder.svg?height=200&width=300",
    },
  ]

  // Filter contractors based on current filters
  useEffect(() => {
    setIsLoading(true)

    // Simulate API call delay
    setTimeout(() => {
      const filtered = allContractors.filter((contractor) => {
        // Filter by search query
        if (
          filters.query &&
          !contractor.name.toLowerCase().includes(filters.query.toLowerCase()) &&
          !contractor.specialty.toLowerCase().includes(filters.query.toLowerCase())
        ) {
          return false
        }

        // Filter by category
        if (filters.category !== "all" && contractor.specialty.toLowerCase() !== filters.category.toLowerCase()) {
          return false
        }

        // Filter by price range
        if (contractor.price < filters.priceRange[0] || contractor.price > filters.priceRange[1]) {
          return false
        }

        // Filter by distance
        if (contractor.distance > filters.distance) {
          return false
        }

        // Filter by rating
        if (contractor.rating < filters.rating) {
          return false
        }

        // Filter by verification status
        if (filters.verified && !contractor.verified) {
          return false
        }

        return true
      })

      setResults(filtered)
      setIsLoading(false)
    }, 500)
  }, [filters])

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      query: "",
      category: "all",
      priceRange: [0, 100000],
      distance: 25,
      rating: 0,
      verified: false,
    })
  }

  const formatPrice = (price: number) => {
    if (price >= 1000) {
      return `$${(price / 1000).toFixed(0)}K`
    }
    return `$${price}`
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-light text-slate-900 mb-2">Find Contractors</h1>
          <p className="text-slate-500">Search for qualified professionals for your renovation project</p>
        </div>

        {/* Search Bar */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by contractor name, specialty, or keyword..."
              value={filters.query}
              onChange={(e) => handleFilterChange("query", e.target.value)}
              className="pl-10 border-slate-200 focus:border-slate-300 bg-white"
            />
          </div>
          <Button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            variant="outline"
            className="bg-white border-slate-200 hover:border-slate-300"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {Object.values(filters).some((value) => {
              if (typeof value === "boolean" && value) return true
              if (Array.isArray(value) && (value[0] > 0 || value[1] < 100000)) return true
              if (typeof value === "number" && value > 0 && value < 25) return true
              if (typeof value === "string" && value !== "" && value !== "all") return true
              return false
            }) && (
              <span className="ml-2 w-5 h-5 rounded-full bg-slate-900 text-white text-xs flex items-center justify-center">
                !
              </span>
            )}
          </Button>
        </div>

        {/* Filters Panel */}
        {isFilterOpen && (
          <div className="bg-white border border-slate-200 rounded-xl p-6 mb-8 shadow-sm animate-in">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-slate-900">Search Filters</h2>
              <Button variant="ghost" size="sm" onClick={clearFilters} className="text-slate-500 hover:text-slate-700">
                Clear All
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Category Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Category</h3>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => handleFilterChange("category", category.id)}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        filters.category === category.id
                          ? "bg-slate-900 text-white"
                          : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                      }`}
                    >
                      {category.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">
                  Price Range: {formatPrice(filters.priceRange[0])} - {formatPrice(filters.priceRange[1])}
                </h3>
                <div className="px-2">
                  <Slider
                    defaultValue={[0, 100000]}
                    min={0}
                    max={100000}
                    step={1000}
                    value={filters.priceRange}
                    onValueChange={(value) => handleFilterChange("priceRange", value)}
                    className="mb-6"
                  />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>$0</span>
                    <span>$25K</span>
                    <span>$50K</span>
                    <span>$75K</span>
                    <span>$100K+</span>
                  </div>
                </div>
              </div>

              {/* Distance Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Distance: {filters.distance} miles</h3>
                <div className="px-2">
                  <Slider
                    defaultValue={[25]}
                    min={1}
                    max={50}
                    step={1}
                    value={[filters.distance]}
                    onValueChange={(value) => handleFilterChange("distance", value[0])}
                    className="mb-6"
                  />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>1 mile</span>
                    <span>25 miles</span>
                    <span>50 miles</span>
                  </div>
                </div>
              </div>

              {/* Rating Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Minimum Rating: {filters.rating}+ stars</h3>
                <div className="flex items-center space-x-2">
                  {[0, 3, 3.5, 4, 4.5, 5].map((rating) => (
                    <button
                      key={rating}
                      onClick={() => handleFilterChange("rating", rating)}
                      className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                        filters.rating === rating
                          ? "bg-slate-900 text-white"
                          : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                      }`}
                    >
                      {rating === 0 ? "Any" : rating}
                    </button>
                  ))}
                </div>
              </div>

              {/* Verified Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Verification</h3>
                <button
                  onClick={() => handleFilterChange("verified", !filters.verified)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                    filters.verified ? "bg-slate-900 text-white" : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                  }`}
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>Verified Contractors Only</span>
                </button>
              </div>
            </div>

            <div className="flex justify-end mt-8">
              <Button onClick={() => setIsFilterOpen(false)} className="bg-slate-900 hover:bg-slate-800 text-white">
                Apply Filters
              </Button>
            </div>
          </div>
        )}

        {/* Results Count & Sort */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-slate-500">{isLoading ? "Searching..." : `${results.length} contractors found`}</p>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-500">Sort by:</span>
            <select className="text-sm border-none bg-transparent focus:outline-none text-slate-900 font-medium">
              <option>Relevance</option>
              <option>Rating: High to Low</option>
              <option>Distance: Near to Far</option>
              <option>Price: Low to High</option>
            </select>
          </div>
        </div>

        {/* Results Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white border border-slate-100 rounded-xl overflow-hidden animate-pulse">
                <div className="h-48 bg-slate-200" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-slate-200 rounded w-3/4" />
                  <div className="h-4 bg-slate-200 rounded w-1/2" />
                  <div className="h-4 bg-slate-200 rounded w-full" />
                  <div className="h-10 bg-slate-200 rounded" />
                </div>
              </div>
            ))}
          </div>
        ) : results.length === 0 ? (
          <div className="bg-white border border-slate-100 rounded-xl p-12 text-center">
            <X className="h-12 w-12 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">No contractors found</h3>
            <p className="text-slate-500 mb-6">Try adjusting your filters or search terms</p>
            <Button onClick={clearFilters} variant="outline" className="bg-transparent border-slate-200">
              Clear All Filters
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {results.map((contractor) => (
              <div
                key={contractor.id}
                className="bg-white border border-slate-100 rounded-xl overflow-hidden hover:shadow-md transition-all duration-200"
              >
                {/* Image */}
                <div className="relative h-48 bg-slate-100 overflow-hidden">
                  <img
                    src={contractor.image || "/placeholder.svg"}
                    alt={contractor.name}
                    className="w-full h-full object-cover"
                  />
                  {contractor.verified && (
                    <div className="absolute top-3 left-3 bg-slate-900 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </div>
                  )}
                  <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-slate-900 px-2 py-1 rounded-full text-xs font-medium">
                    {contractor.priceLabel}
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-medium text-slate-900 mb-1">{contractor.name}</h3>
                  <p className="text-sm text-slate-600 mb-3">{contractor.specialty}</p>

                  <div className="flex items-center justify-between mb-4 text-sm">
                    <div className="flex items-center">
                      <Star className="h-3 w-3 text-yellow-400 fill-current mr-1" />
                      <span className="text-slate-700">
                        {contractor.rating} ({contractor.reviewCount})
                      </span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-3 w-3 text-slate-400 mr-1" />
                      <span className="text-slate-500">{contractor.distance} miles</span>
                    </div>
                  </div>

                  <Link href={`/contractors/${contractor.id}`}>
                    <Button className="w-full bg-slate-900 hover:bg-slate-800 text-white">View Profile</Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Mobile Filter Button */}
        <div className="md:hidden fixed bottom-6 right-6">
          <Button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="h-14 w-14 rounded-full bg-slate-900 hover:bg-slate-800 text-white shadow-lg"
          >
            <SlidersHorizontal className="h-6 w-6" />
          </Button>
        </div>
      </div>
    </div>
  )
}
