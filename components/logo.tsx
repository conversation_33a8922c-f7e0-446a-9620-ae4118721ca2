import Link from "next/link"

interface LogoProps {
  variant?: "default" | "light" | "dark" | "minimal"
  size?: "xs" | "sm" | "md" | "lg" | "xl"
  showText?: boolean
  className?: string
}

// Professional RenovHub Logo SVG
function LogoIcon({ size, variant }: { size: string; variant: string }) {
  const iconColor = variant === "light" ? "#ffffff" :
                   variant === "dark" ? "#1e293b" :
                   "#3b82f6"

  return (
    <svg
      viewBox="0 0 40 40"
      className={size}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Gradient Definition */}
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3b82f6" />
          <stop offset="100%" stopColor="#1d4ed8" />
        </linearGradient>
      </defs>

      {/* Background Circle */}
      <circle
        cx="20"
        cy="20"
        r="18"
        fill={variant === "light" ? "#ffffff" : variant === "dark" ? "#1e293b" : "url(#logoGradient)"}
        className="drop-shadow-lg"
      />

      {/* House Structure */}
      <path
        d="M12 28V18L20 12L28 18V28H24V22H16V28H12Z"
        fill="white"
        stroke="white"
        strokeWidth="0.5"
        strokeLinejoin="round"
      />

      {/* Hammer Tool */}
      <path
        d="M25 15L27 13L29 15L27 17L25 15Z"
        fill="white"
        stroke="white"
        strokeWidth="0.5"
      />
      <path
        d="M24 16L26 14L26.5 14.5L24.5 16.5L24 16Z"
        fill="white"
      />

      {/* Window */}
      <rect
        x="17"
        y="16"
        width="6"
        height="4"
        fill={iconColor}
        rx="0.5"
      />

      {/* Door */}
      <rect
        x="18"
        y="24"
        width="4"
        height="4"
        fill={iconColor}
        rx="0.5"
      />
    </svg>
  )
}

export function Logo({ variant = "default", size = "md", showText = true, className = "" }: LogoProps) {
  const textColor = variant === "light" ? "text-white" :
                   variant === "dark" ? "text-slate-900" :
                   ""

  const sizeConfig = {
    xs: {
      text: "text-sm",
      icon: "h-6 w-6",
      spacing: "space-x-1.5"
    },
    sm: {
      text: "text-base",
      icon: "h-7 w-7",
      spacing: "space-x-2"
    },
    md: {
      text: "text-lg",
      icon: "h-8 w-8",
      spacing: "space-x-2"
    },
    lg: {
      text: "text-xl",
      icon: "h-10 w-10",
      spacing: "space-x-3"
    },
    xl: {
      text: "text-2xl",
      icon: "h-12 w-12",
      spacing: "space-x-3"
    }
  }

  const config = sizeConfig[size]

  return (
    <Link
      href="/"
      className={`flex items-center ${config.spacing} font-medium ${textColor} hover:opacity-80 transition-opacity ${className}`}
    >
      <LogoIcon size={config.icon} variant={variant} />
      {showText && (
        <span className={`${config.text} tracking-wide ${
          variant === "light"
            ? "text-white"
            : variant === "dark"
            ? "text-slate-900"
            : "text-slate-800"
        } transition-colors duration-200`}
        style={{
          fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
          fontWeight: 400,
          letterSpacing: '0.025em',
          fontFeatureSettings: '"cv02", "cv03", "cv04", "cv11"'
        }}>
          RenovHub
        </span>
      )}
    </Link>
  )
}

// Logo variants for different use cases
export function LogoMark({ size = "md", variant = "default", className = "" }: Omit<LogoProps, "showText">) {
  return <Logo size={size} variant={variant} showText={false} className={className} />
}

export function LogoFull({ size = "md", variant = "default", className = "" }: Omit<LogoProps, "showText">) {
  return <Logo size={size} variant={variant} showText={true} className={className} />
}
