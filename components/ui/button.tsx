import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus-visible:ring-blue-500 shadow-lg shadow-blue-500/25 hover:shadow-xl hover:scale-105",
        destructive: "bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 focus-visible:ring-red-500 shadow-lg shadow-red-500/25 hover:shadow-xl hover:scale-105",
        outline: "border-2 border-slate-200 bg-white/80 backdrop-blur-sm text-slate-700 hover:border-slate-300 hover:bg-white focus-visible:ring-slate-500 shadow-sm hover:shadow-md hover:scale-105",
        secondary: "bg-gradient-to-r from-slate-100 to-slate-200 text-slate-900 hover:from-slate-200 hover:to-slate-300 focus-visible:ring-slate-500 shadow-sm hover:shadow-md hover:scale-105",
        ghost: "text-slate-600 hover:text-slate-900 hover:bg-slate-100 focus-visible:ring-slate-500 hover:scale-105",
        link: "text-blue-600 underline-offset-4 hover:underline hover:text-blue-700 focus-visible:ring-blue-500",
        premium: "bg-gradient-to-r from-emerald-600 to-emerald-700 text-white hover:from-emerald-700 hover:to-emerald-800 focus-visible:ring-emerald-500 shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:scale-105",
        gradient: "bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 text-white hover:shadow-xl hover:scale-105 focus-visible:ring-purple-500 shadow-lg",
      },
      size: {
        default: "h-11 px-6 py-3 text-sm rounded-xl min-h-[44px]",
        sm: "h-9 px-4 py-2 text-sm rounded-lg min-h-[36px]",
        lg: "h-12 px-8 py-3 text-base rounded-xl min-h-[48px]",
        xl: "h-14 px-10 py-4 text-lg rounded-2xl min-h-[56px]",
        icon: "h-11 w-11 rounded-xl min-h-[44px] min-w-[44px]",
        action: "h-9 px-3 py-2 text-sm rounded-lg min-h-[36px] min-w-[80px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
