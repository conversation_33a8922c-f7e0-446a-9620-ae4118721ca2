"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Check, Copy, Heart, Star, ThumbsUp, Share2, Bookmark, Eye, EyeOff } from "lucide-react"
import { useLikes, useFavorites } from "@/hooks/use-favorites"

// Enhanced Button with hover effect
export function RippleButton({
  children,
  onClick,
  className = "",
  variant = "default",
  ...props
}: any) {
  return (
    <Button
      onClick={onClick}
      className={cn("relative overflow-hidden transition-all duration-200 hover:scale-105 active:scale-95", className)}
      variant={variant}
      {...props}
    >
      {children}
    </Button>
  )
}

// Copy to clipboard button
export function CopyButton({ 
  text, 
  className = "",
  children 
}: { 
  text: string
  className?: string
  children?: React.ReactNode
}) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleCopy}
      className={cn("transition-all duration-200", className)}
    >
      {copied ? (
        <div className="flex items-center space-x-1 text-green-600">
          <Check className="h-4 w-4" />
          <span>Copied!</span>
        </div>
      ) : (
        <div className="flex items-center space-x-1">
          <Copy className="h-4 w-4" />
          {children || <span>Copy</span>}
        </div>
      )}
    </Button>
  )
}

// Like/Heart button with animation
export function LikeButton({
  itemId,
  itemType = "contractor",
  className = ""
}: {
  itemId: string
  itemType?: "contractor" | "project" | "review"
  className?: string
}) {
  const { isLiked, likeCount, isLoading, toggleLike } = useLikes({
    itemId,
    itemType,
    enableOffline: true
  })

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLike}
      disabled={isLoading}
      className={cn("group transition-all duration-200 active:scale-95", className)}
    >
      <div className="flex items-center space-x-1">
        <Heart
          className={cn(
            "h-4 w-4 transition-colors duration-200",
            isLiked ? "fill-red-500 text-red-500" : "text-slate-500 group-hover:text-red-500",
            isLoading && "opacity-50"
          )}
        />
        <span
          className={cn(
            "text-sm transition-colors duration-200",
            isLiked ? "text-red-500" : "text-slate-500",
            isLoading && "opacity-50"
          )}
        >
          {likeCount}
        </span>
      </div>
    </Button>
  )
}

// Bookmark button with animation
export function BookmarkButton({
  itemId,
  itemType = "contractor",
  className = ""
}: {
  itemId: string
  itemType?: "contractor" | "project"
  className?: string
}) {
  const { isFavorite, isLoading, toggleFavorite } = useFavorites({
    itemId,
    itemType,
    enableOffline: true
  })

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleFavorite}
      disabled={isLoading}
      className={cn("group transition-all duration-200 active:scale-95", className)}
    >
      <Bookmark
        className={cn(
          "h-4 w-4 transition-colors duration-200",
          isFavorite ? "fill-blue-500 text-blue-500" : "text-slate-500 group-hover:text-blue-500",
          isLoading && "opacity-50"
        )}
      />
    </Button>
  )
}

// Star rating component
export function StarRating({
  rating = 0,
  maxRating = 5,
  onRatingChange,
  readonly = false,
  size = "md",
  className = ""
}: {
  rating?: number
  maxRating?: number
  onRatingChange?: (rating: number) => void
  readonly?: boolean
  size?: "sm" | "md" | "lg"
  className?: string
}) {
  const [hoverRating, setHoverRating] = useState(0)
  const [currentRating, setCurrentRating] = useState(rating)

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  }

  const handleClick = (newRating: number) => {
    if (readonly) return
    setCurrentRating(newRating)
    onRatingChange?.(newRating)
  }

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      {Array.from({ length: maxRating }, (_, index) => {
        const starValue = index + 1
        const isFilled = starValue <= (hoverRating || currentRating)

        return (
          <button
            key={index}
            onClick={() => handleClick(starValue)}
            onMouseEnter={() => !readonly && setHoverRating(starValue)}
            onMouseLeave={() => !readonly && setHoverRating(0)}
            disabled={readonly}
            className={cn(
              "transition-all duration-200",
              readonly ? "cursor-default" : "cursor-pointer hover:scale-110 active:scale-90"
            )}
          >
            <Star
              className={cn(
                sizeClasses[size],
                isFilled
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-slate-300 hover:text-yellow-400"
              )}
            />
          </button>
        )
      })}
    </div>
  )
}

// Enhanced rating display with counts - Grouped as preferred
export function RatingDisplay({
  rating = 0,
  reviewCount,
  bidCount,
  size = "sm",
  showCounts = true,
  className = ""
}: {
  rating?: number
  reviewCount?: number
  bidCount?: number
  size?: "sm" | "md" | "lg"
  showCounts?: boolean
  className?: string
}) {
  const sizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  }

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  }

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <div className="flex items-center space-x-1">
        <Star
          className={cn(
            sizeClasses[size],
            "fill-yellow-400 text-yellow-400"
          )}
        />
        <span className={cn("font-medium text-slate-900", textSizeClasses[size])}>
          {rating.toFixed(1)}
        </span>
      </div>

      {showCounts && (reviewCount !== undefined || bidCount !== undefined) && (
        <div className={cn("flex items-center space-x-1 text-slate-500", textSizeClasses[size])}>
          {reviewCount !== undefined && (
            <span>({reviewCount})</span>
          )}
          {bidCount !== undefined && (
            <span>• {bidCount} bids</span>
          )}
        </div>
      )}
    </div>
  )
}

// Vertical Action Buttons - Like and Bookmark stacked as preferred
export function VerticalActionButtons({
  itemId,
  itemType = "contractor",
  className = ""
}: {
  itemId: string
  itemType?: "contractor" | "project" | "review"
  className?: string
}) {
  return (
    <div className={cn("flex flex-col space-y-2", className)}>
      <LikeButton
        itemId={itemId}
        itemType={itemType}
        className="action-button-top-right"
      />
      <BookmarkButton
        itemId={itemId}
        itemType={itemType}
        className="action-button-top-right"
      />
    </div>
  )
}

// Toggle switch with animation
export function AnimatedToggle({ 
  checked = false,
  onToggle,
  label,
  className = ""
}: {
  checked?: boolean
  onToggle?: (checked: boolean) => void
  label?: string
  className?: string
}) {
  const [isChecked, setIsChecked] = useState(checked)

  const handleToggle = () => {
    const newChecked = !isChecked
    setIsChecked(newChecked)
    onToggle?.(newChecked)
  }

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <button
        onClick={handleToggle}
        className={cn(
          "relative w-12 h-6 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-brand-primary focus:ring-offset-2",
          isChecked ? "bg-brand-primary" : "bg-slate-300"
        )}
      >
        <div
          className={cn(
            "absolute top-1 w-4 h-4 bg-white rounded-full shadow-sm transition-transform duration-200",
            isChecked ? "translate-x-6" : "translate-x-0.5"
          )}
        />
      </button>
      {label && (
        <span className="text-sm font-medium text-slate-700">{label}</span>
      )}
    </div>
  )
}

// Floating Action Button
export function FloatingActionButton({ 
  children,
  onClick,
  className = "",
  position = "bottom-right"
}: {
  children: React.ReactNode
  onClick?: () => void
  className?: string
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
}) {
  const positionClasses = {
    "bottom-right": "bottom-6 right-6",
    "bottom-left": "bottom-6 left-6",
    "top-right": "top-6 right-6",
    "top-left": "top-6 left-6"
  }

  return (
    <button
      onClick={onClick}
      className={cn(
        "fixed z-50 w-14 h-14 bg-brand-primary text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-110 active:scale-90",
        positionClasses[position],
        className
      )}
    >
      {children}
    </button>
  )
}

// Progress indicator with animation
export function AnimatedProgress({ 
  progress = 0,
  className = "",
  showPercentage = false
}: {
  progress?: number
  className?: string
  showPercentage?: boolean
}) {
  return (
    <div className={cn("w-full", className)}>
      <div className="w-full bg-slate-200 rounded-full h-2 overflow-hidden">
        <div
          className="h-full bg-brand-primary rounded-full transition-all duration-500 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {showPercentage && (
        <div className="text-right mt-1">
          <span className="text-sm text-slate-600">{Math.round(progress)}%</span>
        </div>
      )}
    </div>
  )
}
