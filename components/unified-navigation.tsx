"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import {
  Menu,
  X,
  Home,
  Briefcase,
  MessageCircle,
  User,
  Bell,
  Hammer,
  Users,
  Settings,
  LogOut,
  Search,
  Plus,
  BarChart3,
  Calendar,
  FileText,
  DollarSign,
  Shield,
  CheckCircle,
  Clock
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { She<PERSON>, <PERSON>etContent, SheetTrigger } from "@/components/ui/sheet"
import { Logo } from "@/components/logo"
import { ProfileIconWithBadge } from "@/components/ui/verified-badge"
import { useUser } from "@/contexts/user-context"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface NavigationItem {
  icon: any
  label: string
  href: string
  count?: number
  badge?: string
  isActive?: boolean
  subItems?: NavigationItem[]
}

export function UnifiedNavigation() {
  const [isOpen, setIsOpen] = useState(false)
  const { user, logout, notifications } = useUser()
  const router = useRouter()
  const pathname = usePathname()

  const unreadNotifications = (notifications || []).filter(n => !n.read).length

  // Customer Navigation Items
  const customerNavItems: NavigationItem[] = [
    { icon: Home, label: "Dashboard", href: "/dashboard" },
    {
      icon: Hammer,
      label: "Projects",
      href: "/projects",
      subItems: [
        { icon: Plus, label: "Create Project", href: "/project/create" },
        { icon: Briefcase, label: "My Projects", href: "/projects" },
        { icon: BarChart3, label: "Analytics", href: "/projects/analytics" }
      ]
    },
    { icon: Users, label: "Contractors", href: "/contractors" },
    {
      icon: MessageCircle,
      label: "Messages",
      href: "/messages",
      ...(unreadNotifications > 0 && { count: unreadNotifications })
    },
    {
      icon: Bell,
      label: "Notifications",
      href: "/notifications",
      ...(unreadNotifications > 0 && { count: unreadNotifications })
    },
  ]

  // Pro/Contractor Navigation Items
  const proNavItems: NavigationItem[] = [
    { icon: Home, label: "Dashboard", href: "/pro/dashboard" },
    {
      icon: Briefcase,
      label: "My Projects",
      href: "/pro/projects",
      subItems: [
        { icon: Clock, label: "Active", href: "/pro/projects?status=active" },
        { icon: FileText, label: "Bids", href: "/pro/bids" },
        { icon: CheckCircle, label: "Completed", href: "/pro/projects?status=completed" }
      ]
    },
    { icon: Calendar, label: "Schedule", href: "/pro/schedule" },
    {
      icon: MessageCircle,
      label: "Messages",
      href: "/messages",
      ...(unreadNotifications > 0 && { count: unreadNotifications })
    },
    { icon: BarChart3, label: "Analytics", href: "/pro/analytics" },
  ]

  const navigationItems = user?.role === 'pro' ? proNavItems : customerNavItems

  // Mark active items based on current path
  const itemsWithActiveState = navigationItems.map(item => ({
    ...item,
    isActive: pathname === item.href || pathname.startsWith(item.href + '/'),
    subItems: item.subItems?.map(subItem => ({
      ...subItem,
      isActive: pathname === subItem.href || pathname.startsWith(subItem.href + '/')
    }))
  }))

  const handleLogout = async () => {
    await logout()
    router.push('/')
    setIsOpen(false)
  }

  const NavItem = ({ item, isMobile = false }: { item: NavigationItem; isMobile?: boolean }) => {
    const baseClasses = isMobile
      ? "flex items-center space-x-3 px-4 py-3.5 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-xl transition-all duration-200 group min-h-[48px]"
      : "relative flex items-center space-x-2 text-slate-600 hover:text-slate-900 transition-all duration-200 text-sm font-medium py-2 px-3 rounded-lg hover:bg-slate-50 group whitespace-nowrap"

    const activeClasses = item.isActive
      ? "text-slate-900 bg-slate-100 font-semibold"
      : ""

    return (
      <Link
        href={item.href}
        className={cn(baseClasses, activeClasses)}
        onClick={() => isMobile && setIsOpen(false)}
      >
        {isMobile && (
          <item.icon className={cn(
            "h-5 w-5 transition-all duration-200",
            item.isActive
              ? "text-white"
              : "text-slate-500 group-hover:text-slate-700"
          )} />
        )}
        <span className="font-medium">{item.label}</span>
        {item.count && item.count > 0 && (
          <span className={cn(
            "ml-auto text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold transition-all duration-200",
            item.isActive
              ? "bg-white/20 text-white"
              : "bg-red-500 text-white shadow-sm"
          )}>
            {item.count > 9 ? '9+' : item.count}
          </span>
        )}
        {item.badge && (
          <span className="ml-auto bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">
            {item.badge}
          </span>
        )}
      </Link>
    )
  }

  return (
    <>
      {/* Desktop Navigation */}
      <nav id="navigation" className="hidden md:flex items-center justify-between px-6 lg:px-8 py-3 border-b border-slate-100 bg-white/80 backdrop-blur-xl sticky top-0 z-50" role="navigation" aria-label="Main navigation">
        <div className="flex items-center space-x-4">
          {/* Enhanced Logo Section - Always Visible */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            <Logo size="md" />
            {user?.role === 'pro' && (
              <div className="flex items-center space-x-1.5 px-2.5 py-1 bg-emerald-500 rounded-full text-white text-xs font-medium">
                <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                <span className="text-xs font-medium text-white">Pro</span>
              </div>
            )}
          </div>

          {/* Navigation Items - Responsive */}
          {user && (
            <div className="flex items-center space-x-4 overflow-x-auto scrollbar-hide">
              {itemsWithActiveState.map((item) => (
                <NavItem key={item.label} item={item} />
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {user ? (
            <>
              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                {user.role === 'customer' && (
                  <Button size="sm" asChild className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg shadow-blue-500/25 rounded-xl font-semibold px-4 py-2.5 transition-all duration-200 hover:scale-105">
                    <Link href="/project/create">
                      <Plus className="h-4 w-4 mr-2" />
                      New Project
                    </Link>
                  </Button>
                )}

                {user.role === 'pro' && (
                  <Button size="sm" asChild className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white shadow-lg shadow-emerald-500/25 rounded-xl font-semibold px-4 py-2.5 transition-all duration-200 hover:scale-105">
                    <Link href="/pro/browse">
                      <Search className="h-4 w-4 mr-2" />
                      Find Projects
                    </Link>
                  </Button>
                )}
              </div>

              {/* Notifications */}
              <Link href="/notifications" className="relative p-3 rounded-xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-slate-100/50 transition-all duration-200 group">
                <Bell className="h-5 w-5 text-slate-600 group-hover:text-slate-700 transition-colors duration-200" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse">
                    {unreadNotifications > 9 ? '9+' : unreadNotifications}
                  </span>
                )}
              </Link>

              {/* User Menu */}
              <div className="flex items-center space-x-4 pl-6 border-l border-slate-200/60">


                <Link
                  href="/profile"
                  className="text-slate-600 hover:text-slate-900 transition-all duration-200 px-4 py-2.5 rounded-xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-slate-100/50 group"
                >
                  <ProfileIconWithBadge
                    user={{
                      name: user.name,
                      role: user.role,
                      isVerified: user.verified || false,
                      isOnline: true // You can implement real online status logic here
                    }}
                    className="group-hover:scale-105 transition-transform duration-200"
                  />
                </Link>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                  className="text-slate-500 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 p-2.5"
                  title="Sign out"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-4">
              <Button variant="ghost" asChild className="rounded-xl hover:bg-slate-100 transition-all duration-200 font-medium text-slate-600 hover:text-slate-900">
                <Link href="/login">Sign in</Link>
              </Button>
              <Button asChild className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg shadow-blue-500/25 font-semibold px-6 py-2.5 transition-all duration-200 hover:scale-105">
                <Link href="/register">Get Started</Link>
              </Button>
            </div>
          )}
        </div>
      </nav>

      {/* Enhanced Mobile Navigation */}
      <nav className="md:hidden flex items-center justify-between px-4 py-4 border-b border-slate-200/60 bg-white/95 backdrop-blur-md sticky top-0 z-50 shadow-lg">
        {/* Enhanced Mobile Logo Section - Always Prominent */}
        <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
          <Logo size="md" />
          {user?.role === 'pro' && (
            <div className="flex items-center space-x-1.5 px-2.5 py-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full shadow-md shadow-emerald-500/25">
              <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
              <span className="text-xs font-bold text-white uppercase tracking-wide">Pro</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {user && (
            <>
              {/* Notifications */}
              <Link href="/notifications" className="relative p-2.5 rounded-xl hover:bg-slate-50 transition-all duration-200">
                <Bell className="h-5 w-5 text-slate-600" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-0.5 -right-0.5 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse">
                    {unreadNotifications > 9 ? '9+' : unreadNotifications}
                  </span>
                )}
              </Link>

              {/* Mobile Menu - Enhanced Side Navigation */}
              <Sheet open={isOpen} onOpenChange={setIsOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2.5 rounded-xl hover:bg-slate-50 transition-all duration-200">
                    <Menu className="h-5 w-5 text-slate-600" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80 bg-gradient-to-br from-white to-slate-50/50 overflow-y-auto">
                  <div className="flex flex-col h-full">
                    {/* Header */}
                    <div className="flex items-center justify-between pb-6 border-b border-slate-200/60">
                      <div className="flex items-center space-x-3">
                        <Logo />
                        {user?.role === 'pro' && (
                          <div className="flex items-center space-x-1.5 px-2 py-1 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full">
                            <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                            <span className="text-xs font-bold text-white uppercase tracking-wide">Pro</span>
                          </div>
                        )}
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)} className="rounded-xl hover:bg-slate-100 transition-all duration-200">
                        <X className="h-5 w-5 text-slate-500" />
                      </Button>
                    </div>

                    {/* User Info */}
                    <div className="py-6 border-b border-slate-200/60">
                      <div className="flex items-center space-x-4">
                        <div className="w-14 h-14 bg-gradient-to-br from-slate-600 to-slate-700 rounded-full flex items-center justify-center shadow-lg">
                          <User className="h-7 w-7 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-slate-900 text-lg">{user.name}</p>
                          <p className="text-sm text-slate-500 capitalize font-medium">{user.role}</p>
                          {user?.role === 'pro' && user.verified && (
                            <div className="flex items-center space-x-1 mt-1">
                              <CheckCircle className="h-4 w-4 text-emerald-600" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Navigation Items */}
                    <div className="flex-1 py-6 space-y-2">
                      {itemsWithActiveState.map((item) => (
                        <div key={item.label}>
                          <NavItem item={item} isMobile />
                          {item.subItems && (
                            <div className="ml-8 mt-2 space-y-1">
                              {item.subItems.map((subItem) => (
                                <NavItem key={subItem.label} item={subItem} isMobile />
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Footer Actions */}
                    <div className="pt-6 border-t border-slate-200/60 space-y-2">
                      <Link
                        href="/profile"
                        className="flex items-center space-x-3 px-4 py-3 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-xl transition-all duration-200"
                        onClick={() => setIsOpen(false)}
                      >
                        <Settings className="h-5 w-5" />
                        <span className="font-medium">Settings</span>
                      </Link>

                      <button
                        onClick={handleLogout}
                        className="flex items-center space-x-3 px-4 py-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all duration-200 w-full"
                      >
                        <LogOut className="h-5 w-5" />
                        <span className="font-medium">Sign out</span>
                      </button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </>
          )}
          
          {!user && (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" asChild className="rounded-xl hover:bg-slate-100 transition-all duration-200 font-medium">
                <Link href="/login">Sign in</Link>
              </Button>
              <Button size="sm" asChild className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg shadow-blue-500/25 font-semibold transition-all duration-200 hover:scale-105">
                <Link href="/register">Sign up</Link>
              </Button>
            </div>
          )}
        </div>
      </nav>
    </>
  )
}
