"use client"

import { useState, useEffect, useCallback } from 'react'
import { projectService } from '@/services/database'
import { useUser } from '@/contexts/user-context'
import { useToast } from '@/components/ui/toast-system'
import type { Tables } from '@/lib/supabase'

export interface UseProjectsOptions {
  autoFetch?: boolean
  includeCompleted?: boolean
}

export interface UseProjectsReturn {
  projects: Tables<'projects'>[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createProject: (projectData: any) => Promise<Tables<'projects'> | null>
  updateProject: (id: string, updates: Partial<Tables<'projects'>>) => Promise<Tables<'projects'> | null>
  deleteProject: (id: string) => Promise<boolean>
}

export function useProjects(options: UseProjectsOptions = {}): UseProjectsReturn {
  const { autoFetch = true, includeCompleted = false } = options
  const { user } = useUser()
  const { error: showError, success: showSuccess } = useToast()
  
  const [projects, setProjects] = useState<Tables<'projects'>[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProjects = useCallback(async () => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      let response
      if (user.role === 'customer') {
        response = await projectService.findByCustomerId(user.id)
      } else {
        response = await projectService.findByContractorId(user.id)
      }

      if (response.success && response.data) {
        let filteredProjects = response.data
        
        if (!includeCompleted) {
          filteredProjects = response.data.filter(p => p.status !== 'completed' && p.status !== 'cancelled')
        }
        
        setProjects(filteredProjects)
      } else {
        setError(response.error || 'Failed to fetch projects')
        showError(response.error || 'Failed to fetch projects')
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while fetching projects'
      setError(errorMessage)
      showError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [user, includeCompleted, showError])

  const createProject = useCallback(async (projectData: any): Promise<Tables<'projects'> | null> => {
    if (!user) {
      showError('You must be logged in to create a project')
      return null
    }

    setLoading(true)
    try {
      const response = await projectService.create({
        ...projectData,
        customer_id: user.id,
        status: 'draft'
      })

      if (response.success && response.data) {
        setProjects(prev => [response.data!, ...prev])
        showSuccess('Project created successfully')
        return response.data
      } else {
        showError(response.error || 'Failed to create project')
        return null
      }
    } catch (err) {
      showError('An unexpected error occurred while creating the project')
      return null
    } finally {
      setLoading(false)
    }
  }, [user, showError, showSuccess])

  const updateProject = useCallback(async (id: string, updates: Partial<Tables<'projects'>>): Promise<Tables<'projects'> | null> => {
    setLoading(true)
    try {
      const response = await projectService.update(id, updates)

      if (response.success && response.data) {
        setProjects(prev => prev.map(p => p.id === id ? response.data! : p))
        showSuccess('Project updated successfully')
        return response.data
      } else {
        showError(response.error || 'Failed to update project')
        return null
      }
    } catch (err) {
      showError('An unexpected error occurred while updating the project')
      return null
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  const deleteProject = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    try {
      const response = await projectService.delete(id)

      if (response.success) {
        setProjects(prev => prev.filter(p => p.id !== id))
        showSuccess('Project deleted successfully')
        return true
      } else {
        showError(response.error || 'Failed to delete project')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while deleting the project')
      return false
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  useEffect(() => {
    if (autoFetch && user) {
      fetchProjects()
    }
  }, [autoFetch, user, fetchProjects])

  return {
    projects,
    loading,
    error,
    refetch: fetchProjects,
    createProject,
    updateProject,
    deleteProject
  }
}
