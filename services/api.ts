import { ApiResponse, ApiError } from '@/types'
import { supabase } from '@/lib/supabase'
import {
  userService,
  contractorService,
  projectService,
  bidService,
  messageService,
  conversationService,
  reviewService,
  paymentService
} from './database'

// Base API configuration - now using Supabase services
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'

class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    }

    // Add auth token if available
    const token = await this.getAuthToken()
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: token,
      }
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: {
            code: response.status.toString(),
            message: data.message || 'An error occurred',
            details: data.details,
          },
        }
      }

      return {
        success: true,
        data: data.data || data,
        meta: data.meta,
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : 'Network error occurred',
        },
      }
    }
  }

  private async getAuthToken(): Promise<string | null> {
    // Get token from Supabase session
    const { data: { session } } = await supabase.auth.getSession()
    return session?.access_token ? `Bearer ${session.access_token}` : null
  }

  setAuthToken(token: string): void {
    // Token management is handled by Supabase
    console.log('Token management is handled by Supabase')
  }

  clearAuthToken(): void {
    // Token management is handled by Supabase
    console.log('Token management is handled by Supabase')
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint
    return this.request<T>(url, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // Supabase-specific methods
  async uploadToStorage(bucket: string, path: string, file: File): Promise<ApiResponse<{ path: string; url: string }>> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false,
        })

      if (error) {
        return {
          success: false,
          error: {
            code: 'UPLOAD_ERROR',
            message: error.message,
          },
        }
      }

      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(data.path)

      return {
        success: true,
        data: {
          path: data.path,
          url: urlData.publicUrl,
        },
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPLOAD_ERROR',
          message: error instanceof Error ? error.message : 'Upload error occurred',
        },
      }
    }
  }

  // File upload (legacy method for compatibility)
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value.toString())
      })
    }

    const token = this.getAuthToken()
    const headers: Record<string, string> = {}
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: {
            code: response.status.toString(),
            message: data.message || 'Upload failed',
            details: data.details,
          },
        }
      }

      return {
        success: true,
        data: data.data || data,
        meta: data.meta,
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'UPLOAD_ERROR',
          message: error instanceof Error ? error.message : 'Upload error occurred',
        },
      }
    }
  }

  // Supabase database methods
  async getUsers(filters?: Record<string, any>): Promise<ApiResponse<any[]>> {
    try {
      const data = await userService.findMany(filters)
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getProjects(filters?: Record<string, any>): Promise<ApiResponse<any[]>> {
    try {
      const data = await projectService.findMany(filters)
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getContractors(filters?: Record<string, any>): Promise<ApiResponse<any[]>> {
    try {
      const data = await contractorService.findMany(filters)
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getBids(projectId?: string, contractorId?: string): Promise<ApiResponse<any[]>> {
    try {
      let data
      if (projectId) {
        data = await bidService.findByProjectId(projectId)
      } else if (contractorId) {
        data = await bidService.findByContractorId(contractorId)
      } else {
        data = await bidService.findMany()
      }
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getMessages(conversationId: string): Promise<ApiResponse<any[]>> {
    try {
      const data = await messageService.findByConversationId(conversationId)
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getConversations(userId: string): Promise<ApiResponse<any[]>> {
    try {
      const data = await conversationService.findByUserId(userId)
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getReviews(revieweeId?: string, projectId?: string): Promise<ApiResponse<any[]>> {
    try {
      let data
      if (revieweeId) {
        data = await reviewService.findByRevieweeId(revieweeId)
      } else if (projectId) {
        data = await reviewService.findByProjectId(projectId)
      } else {
        data = await reviewService.findMany()
      }
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }

  async getPayments(userId: string): Promise<ApiResponse<any[]>> {
    try {
      const data = await paymentService.findByUserId(userId)
      return { success: true, data }
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : 'Database error occurred',
        },
      }
    }
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Error handling utilities
export function handleApiError(error: ApiError): string {
  switch (error.code) {
    case '400':
      return 'Invalid request. Please check your input.'
    case '401':
      return 'You need to log in to access this resource.'
    case '403':
      return 'You do not have permission to access this resource.'
    case '404':
      return 'The requested resource was not found.'
    case '429':
      return 'Too many requests. Please try again later.'
    case '500':
      return 'Server error. Please try again later.'
    case 'NETWORK_ERROR':
      return 'Network error. Please check your connection.'
    default:
      return error.message || 'An unexpected error occurred.'
  }
}

// Response validation utilities
export function isApiSuccess<T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: true; data: T } {
  return response.success && response.data !== undefined
}

export function isApiError<T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: false; error: ApiError } {
  return !response.success && response.error !== undefined
}

// Retry utility for failed requests
export async function retryRequest<T>(
  requestFn: () => Promise<ApiResponse<T>>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<ApiResponse<T>> {
  let lastResponse: ApiResponse<T>
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    lastResponse = await requestFn()
    
    if (lastResponse.success) {
      return lastResponse
    }
    
    // Don't retry on client errors (4xx)
    if (lastResponse.error?.code.startsWith('4')) {
      return lastResponse
    }
    
    // Wait before retrying (exponential backoff)
    if (attempt < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)))
    }
  }
  
  return lastResponse!
}

// Request caching utility
class RequestCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  set(key: string, data: any, ttl: number = 300000): void { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }

  get(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  clear(): void {
    this.cache.clear()
  }

  delete(key: string): void {
    this.cache.delete(key)
  }
}

export const requestCache = new RequestCache()

// Cached request wrapper
export async function cachedRequest<T>(
  key: string,
  requestFn: () => Promise<ApiResponse<T>>,
  ttl?: number
): Promise<ApiResponse<T>> {
  const cached = requestCache.get(key)
  if (cached) {
    return { success: true, data: cached }
  }

  const response = await requestFn()
  if (response.success && response.data) {
    requestCache.set(key, response.data, ttl)
  }

  return response
}
